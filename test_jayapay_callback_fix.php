<?php
/**
 * JayaPay回调修复测试脚本
 * 测试修复后的签名验证功能
 */

// 引入必要的文件
require_once 'application/common/service/JayaPayService.php';

class JayaPayCallbackFixTest
{
    private $jayaPayService;

    public function __construct()
    {
        $this->jayaPayService = new \app\common\service\JayaPayService();
    }

    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "=== JayaPay回调修复测试 ===\n\n";

        // 测试1：验证平台公钥获取
        $this->testGetPlatformPublicKey();

        // 测试2：使用真实回调数据测试签名验证
        $this->testRealCallbackData();

        // 测试3：测试完整的回调处理流程
        $this->testCompleteCallbackFlow();

        echo "\n=== 测试完成 ===\n";
    }

    /**
     * 测试平台公钥获取
     */
    private function testGetPlatformPublicKey()
    {
        echo "1. 测试平台公钥获取:\n";
        
        $platformPublicKey = $this->jayaPayService->getPlatformPublicKey();
        
        if ($platformPublicKey) {
            echo "✅ 平台公钥获取成功\n";
            echo "公钥前50字符: " . substr($platformPublicKey, 0, 50) . "...\n";
        } else {
            echo "❌ 平台公钥获取失败\n";
        }
        
        echo "\n";
    }

    /**
     * 测试真实回调数据
     */
    private function testRealCallbackData()
    {
        echo "2. 测试真实回调数据签名验证:\n";

        // 从日志中提取的真实回调数据
        $callbackData = [
            "msg" => "SUCCESS",
            "code" => "00",
            "method" => "QRIS",
            "orderNum" => "JP20250803150345177359",
            "platOrderNum" => "PT1B168C55D700405A",
            "payFee" => "2250.23",
            "payMoney" => "50005",
            "phone" => "081234567890",
            "name" => "User",
            "platSign" => "ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=",
            "email" => "<EMAIL>",
            "status" => "SUCCESS"
        ];

        echo "订单号: {$callbackData['orderNum']}\n";
        echo "平台订单号: {$callbackData['platOrderNum']}\n";
        echo "金额: {$callbackData['payMoney']}\n";

        // 获取平台公钥
        $platformPublicKey = $this->jayaPayService->getPlatformPublicKey();
        if (!$platformPublicKey) {
            echo "❌ 无法获取平台公钥\n\n";
            return;
        }

        // 测试签名验证
        try {
            $verifyResult = $this->jayaPayService->verifyRSASign($callbackData, $platformPublicKey);
            if ($verifyResult) {
                echo "✅ 签名验证成功\n";
            } else {
                echo "❌ 签名验证失败\n";
            }
        } catch (Exception $e) {
            echo "❌ 签名验证异常: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }

    /**
     * 测试完整的回调处理流程
     */
    private function testCompleteCallbackFlow()
    {
        echo "3. 测试完整回调处理流程:\n";

        // 使用真实回调数据
        $callbackData = [
            "msg" => "SUCCESS",
            "code" => "00",
            "method" => "QRIS",
            "orderNum" => "JP20250803150345177359",
            "platOrderNum" => "PT1B168C55D700405A",
            "payFee" => "2250.23",
            "payMoney" => "50005",
            "phone" => "081234567890",
            "name" => "User",
            "platSign" => "ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=",
            "email" => "<EMAIL>",
            "status" => "SUCCESS"
        ];

        echo "测试订单: {$callbackData['orderNum']}\n";

        try {
            // 测试充值回调处理
            $result = $this->jayaPayService->handleRechargeCallback($callbackData);
            
            if ($result) {
                echo "✅ 回调处理成功\n";
            } else {
                echo "❌ 回调处理失败\n";
            }
        } catch (Exception $e) {
            echo "❌ 回调处理异常: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }

    /**
     * 发送POST请求到本地回调接口
     */
    private function testLocalCallbackEndpoint()
    {
        echo "4. 测试本地回调接口:\n";

        $callbackData = [
            "msg" => "SUCCESS",
            "code" => "00",
            "method" => "QRIS",
            "orderNum" => "JP20250803150345177359",
            "platOrderNum" => "PT1B168C55D700405A",
            "payFee" => "2250.23",
            "payMoney" => "50005",
            "phone" => "081234567890",
            "name" => "User",
            "platSign" => "ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=",
            "email" => "<EMAIL>",
            "status" => "SUCCESS"
        ];

        $url = 'http://localhost/api/transaction/unifiedCallback';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($callbackData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        echo "请求URL: {$url}\n";
        echo "HTTP状态码: {$httpCode}\n";
        echo "响应内容: {$response}\n";

        if ($httpCode === 200 && $response === 'success') {
            echo "✅ 本地回调接口测试成功\n";
        } else {
            echo "❌ 本地回调接口测试失败\n";
        }

        echo "\n";
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new JayaPayCallbackFixTest();
    $test->runAllTests();
} else {
    echo "<pre>";
    $test = new JayaPayCallbackFixTest();
    $test->runAllTests();
    echo "</pre>";
}

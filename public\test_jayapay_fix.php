<?php
/**
 * JayaPay回调修复测试页面
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入必要的文件
require_once '../application/common/service/JayaPayService.php';

echo "<h1>JayaPay回调修复测试</h1>";

try {
    $jayaPayService = new \app\common\service\JayaPayService();
    
    echo "<h2>1. 测试平台公钥获取</h2>";
    $platformPublicKey = $jayaPayService->getPlatformPublicKey();
    
    if ($platformPublicKey) {
        echo "<p style='color: green;'>✅ 平台公钥获取成功</p>";
        echo "<p>公钥前50字符: " . htmlspecialchars(substr($platformPublicKey, 0, 50)) . "...</p>";
    } else {
        echo "<p style='color: red;'>❌ 平台公钥获取失败</p>";
    }
    
    echo "<h2>2. 测试真实回调数据签名验证</h2>";
    
    // 从日志中提取的真实回调数据
    $callbackData = [
        "msg" => "SUCCESS",
        "code" => "00",
        "method" => "QRIS",
        "orderNum" => "JP20250803150345177359",
        "platOrderNum" => "PT1B168C55D700405A",
        "payFee" => "2250.23",
        "payMoney" => "50005",
        "phone" => "081234567890",
        "name" => "User",
        "platSign" => "ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=",
        "email" => "<EMAIL>",
        "status" => "SUCCESS"
    ];
    
    echo "<p>订单号: {$callbackData['orderNum']}</p>";
    echo "<p>平台订单号: {$callbackData['platOrderNum']}</p>";
    echo "<p>金额: {$callbackData['payMoney']}</p>";
    
    if ($platformPublicKey) {
        try {
            // 测试签名验证
            $verifyResult = $jayaPayService->verifyRSASign($callbackData, $platformPublicKey);
            if ($verifyResult) {
                echo "<p style='color: green;'>✅ 签名验证成功</p>";
            } else {
                echo "<p style='color: red;'>❌ 签名验证失败</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ 签名验证异常: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ 无法获取平台公钥，跳过签名验证</p>";
    }
    
    echo "<h2>3. 测试完整回调处理流程</h2>";
    
    try {
        // 测试充值回调处理
        $result = $jayaPayService->handleRechargeCallback($callbackData);
        
        if ($result) {
            echo "<p style='color: green;'>✅ 回调处理成功</p>";
        } else {
            echo "<p style='color: red;'>❌ 回调处理失败</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 回调处理异常: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<h2>4. 配置信息检查</h2>";
    
    // 检查配置文件
    $configPath = '../config/payment_config.php';
    if (file_exists($configPath)) {
        echo "<p style='color: green;'>✅ 配置文件存在</p>";
        
        $config = include($configPath);
        if (isset($config['jaya_pay']['platform_public_key'])) {
            echo "<p style='color: green;'>✅ 平台公钥配置存在</p>";
            echo "<p>平台公钥: " . htmlspecialchars(substr($config['jaya_pay']['platform_public_key'], 0, 50)) . "...</p>";
        } else {
            echo "<p style='color: red;'>❌ 平台公钥配置不存在</p>";
        }
        
        if (isset($config['jaya_pay']['merchants']['default']['public_key'])) {
            echo "<p style='color: blue;'>ℹ️ 商户公钥配置存在</p>";
            echo "<p>商户公钥: " . htmlspecialchars(substr($config['jaya_pay']['merchants']['default']['public_key'], 0, 50)) . "...</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ 配置文件不存在</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 测试异常: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<h2>5. 模拟回调请求测试</h2>";
echo "<p>您可以点击下面的按钮来模拟发送回调请求：</p>";
echo "<button onclick='testCallback()'>测试回调请求</button>";
echo "<div id='callback-result'></div>";

?>

<script>
function testCallback() {
    const callbackData = {
        "msg": "SUCCESS",
        "code": "00",
        "method": "QRIS",
        "orderNum": "JP20250803150345177359",
        "platOrderNum": "PT1B168C55D700405A",
        "payFee": "2250.23",
        "payMoney": "50005",
        "phone": "081234567890",
        "name": "User",
        "platSign": "ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=",
        "email": "<EMAIL>",
        "status": "SUCCESS"
    };
    
    const formData = new FormData();
    for (const key in callbackData) {
        formData.append(key, callbackData[key]);
    }
    
    fetch('/api/transaction/unifiedCallback', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        document.getElementById('callback-result').innerHTML = 
            '<h3>回调请求结果:</h3><pre>' + data + '</pre>';
    })
    .catch(error => {
        document.getElementById('callback-result').innerHTML = 
            '<h3>回调请求错误:</h3><pre>' + error + '</pre>';
    });
}
</script>

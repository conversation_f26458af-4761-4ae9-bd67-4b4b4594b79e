---------------------------------------------------------------

[2025-08-03T15:02:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000755s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001045s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754204521 LIMIT 100 [ RunTime:0.000491s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001604s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000505s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000510s ]
---------------------------------------------------------------

[2025-08-03T15:03:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001074s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001621s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754204581 LIMIT 100 [ RunTime:0.000752s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001857s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000511s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000543s ]
---------------------------------------------------------------

[2025-08-03T15:03:31+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.001068s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001447s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.001077s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001400s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000764s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001800s ]
---------------------------------------------------------------

[2025-08-03T15:03:33+08:00] ************* GET winmalllink.shop/
[ sql ] [ DB ] CONNECT:[ UseTime:0.000583s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001504s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000691s ]
---------------------------------------------------------------

[2025-08-03T15:03:36+08:00] ************** POST www.lotteup.com/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 $_POST[token]: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 $_REQUEST[token]: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 最终user_token: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ sql ] [ DB ] CONNECT:[ UseTime:0.000861s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001881s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1149  AND `username` = '1111111'  AND `state` = 1 [ RunTime:0.000760s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.001479s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1149 , 1754204616 , '[\"lang\",\"token\"]' , '[\"cn\",\"a1e7naANjvp9kPAGhLxhy8LBpkS9a\\/1prGnF3Njirtzvu4fpliw2OjI\"]' , '**************' , 'getuserinfo' , 'User') [ RunTime:0.001091s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1149' LIMIT 1 [ RunTime:0.001087s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001549s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000781s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001324s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754150399  AND `state` = 1 [ RunTime:0.000639s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000654s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000480s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000688s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000566s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000443s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.001102s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1149  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000430s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000478s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000708s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000960s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1149  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000604s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 100  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000438s ]
---------------------------------------------------------------

[2025-08-03T15:03:36+08:00] ************** POST www.lotteup.com/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000394s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.001484s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'cn' ORDER BY `add_time` DESC [ RunTime:0.000544s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001395s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000770s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.001486s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000756s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001998s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000936s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000942s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000834s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000847s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'cn' [ RunTime:0.000439s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000377s ]
---------------------------------------------------------------

[2025-08-03T15:03:36+08:00] ************** POST www.lotteup.com/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 $_POST[token]: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 $_REQUEST[token]: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 最终user_token: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ sql ] [ DB ] CONNECT:[ UseTime:0.000517s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001927s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1149  AND `username` = '1111111'  AND `state` = 1 [ RunTime:0.000579s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.001060s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1149 , 1754204616 , '[\"lang\",\"token\"]' , '[\"cn\",\"a1e7naANjvp9kPAGhLxhy8LBpkS9a\\/1prGnF3Njirtzvu4fpliw2OjI\"]' , '**************' , 'getuserinfo' , 'User') [ RunTime:0.000786s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1149' LIMIT 1 [ RunTime:0.000836s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001428s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000689s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001702s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754150399  AND `state` = 1 [ RunTime:0.000742s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000687s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000585s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000630s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000670s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000553s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.001076s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1149  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000434s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000418s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000472s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.001803s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1149  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000506s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 100  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000469s ]
---------------------------------------------------------------

[2025-08-03T15:03:37+08:00] ************** POST www.lotteup.com/api/Account/getBankCardList
[ info ] BaseController action: getbankcardlist
[ info ] BaseController controller: Account
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 $_POST[token]: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 $_REQUEST[token]: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 最终user_token: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ sql ] [ DB ] CONNECT:[ UseTime:0.001153s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001978s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1149  AND `username` = '1111111'  AND `state` = 1 [ RunTime:0.000704s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.001263s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1149 , ********** , '[\"lang\",\"token\"]' , '[\"cn\",\"a1e7naANjvp9kPAGhLxhy8LBpkS9a\\/1prGnF3Njirtzvu4fpliw2OjI\"]' , '**************' , 'getbankcardlist' , 'Account') [ RunTime:0.001006s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_bank` [ RunTime:0.001403s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_bank` WHERE  `uid` = 1149  AND `status` = 1 ORDER BY `id` DESC [ RunTime:0.000893s ]
---------------------------------------------------------------

[2025-08-03T15:03:37+08:00] ************** POST www.lotteup.com/api/Transaction/getRechargeRecord
[ info ] BaseController action: getrechargerecord
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 $_POST[token]: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 $_REQUEST[token]: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 最终user_token: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ sql ] [ DB ] CONNECT:[ UseTime:0.000732s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001732s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1149  AND `username` = '1111111'  AND `state` = 1 [ RunTime:0.000795s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.001251s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1149 , ********** , '[\"state\",\"page_no\",\"lang\",\"token\"]' , '[\"0\",\"1\",\"cn\",\"a1e7naANjvp9kPAGhLxhy8LBpkS9a\\/1prGnF3Njirtzvu4fpliw2OjI\"]' , '**************' , 'getrechargerecord' , 'Transaction') [ RunTime:0.000860s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001468s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `uid` = 1149 [ RunTime:0.000633s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_recharge` WHERE  `uid` = 1149 ORDER BY `add_time` DESC,`id` DESC LIMIT 0,10 [ RunTime:0.000908s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_bank` [ RunTime:0.001622s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000533s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_rechange_type` [ RunTime:0.001374s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000568s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000487s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 118 LIMIT 1 [ RunTime:0.000430s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000637s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000521s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000528s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 118 LIMIT 1 [ RunTime:0.000585s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000472s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000461s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000289s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000380s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000308s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000310s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000263s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 118 LIMIT 1 [ RunTime:0.000313s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000283s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000257s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000368s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000438s ]
---------------------------------------------------------------

[2025-08-03T15:03:37+08:00] ************** POST www.lotteup.com/api/Transaction/getRechargeRecord
[ info ] BaseController action: getrechargerecord
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 $_POST[token]: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 $_REQUEST[token]: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 最终user_token: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ sql ] [ DB ] CONNECT:[ UseTime:0.000821s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001840s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1149  AND `username` = '1111111'  AND `state` = 1 [ RunTime:0.000547s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000975s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1149 , ********** , '[\"state\",\"page_no\",\"lang\",\"token\"]' , '[\"0\",\"2\",\"cn\",\"a1e7naANjvp9kPAGhLxhy8LBpkS9a\\/1prGnF3Njirtzvu4fpliw2OjI\"]' , '**************' , 'getrechargerecord' , 'Transaction') [ RunTime:0.000577s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001137s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `uid` = 1149 [ RunTime:0.000493s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_recharge` WHERE  `uid` = 1149 ORDER BY `add_time` DESC,`id` DESC LIMIT 10,10 [ RunTime:0.000761s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_bank` [ RunTime:0.001156s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000320s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_rechange_type` [ RunTime:0.000885s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000422s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000501s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000371s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000423s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000307s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000381s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000299s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000397s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000290s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000327s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 118 LIMIT 1 [ RunTime:0.000336s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000353s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 118 LIMIT 1 [ RunTime:0.000288s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000258s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000276s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000230s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000286s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000300s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000377s ]
---------------------------------------------------------------

[2025-08-03T15:03:37+08:00] ************** POST www.lotteup.com/api/Transaction/getPayTypes
[ info ] BaseController action: getpaytypes
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 $_POST[token]: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 $_REQUEST[token]: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 最终user_token: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ sql ] [ DB ] CONNECT:[ UseTime:0.000801s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001698s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1149  AND `username` = '1111111'  AND `state` = 1 [ RunTime:0.000648s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.001328s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1149 , ********** , '[\"country\",\"lang\",\"token\"]' , '[\"ID\",\"cn\",\"a1e7naANjvp9kPAGhLxhy8LBpkS9a\\/1prGnF3Njirtzvu4fpliw2OjI\"]' , '**************' , 'getpaytypes' , 'Transaction') [ RunTime:0.000776s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_rechange_type` [ RunTime:0.001107s ]
[ sql ] [ SQL ] SELECT `id`,`name`,`mode`,`type`,`minPrice`,`maxPrice`,`sort` FROM `ly_rechange_type` WHERE  `state` = 1  AND `type` = 'app' ORDER BY `sort` ASC [ RunTime:0.000554s ]
---------------------------------------------------------------

[2025-08-03T15:03:37+08:00] ************** POST www.lotteup.com/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 $_POST[token]: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 $_REQUEST[token]: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 最终user_token: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ sql ] [ DB ] CONNECT:[ UseTime:0.000928s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001961s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1149  AND `username` = '1111111'  AND `state` = 1 [ RunTime:0.000840s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.001494s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1149 , ********** , '[\"lang\",\"token\"]' , '[\"cn\",\"a1e7naANjvp9kPAGhLxhy8LBpkS9a\\/1prGnF3Njirtzvu4fpliw2OjI\"]' , '**************' , 'getuserinfo' , 'User') [ RunTime:0.001005s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1149' LIMIT 1 [ RunTime:0.001454s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001985s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.001617s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001732s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754150399  AND `state` = 1 [ RunTime:0.000921s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000795s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000801s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000949s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000882s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000497s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.001297s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1149  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000636s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000597s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1149  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000927s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.001383s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1149  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000653s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 100  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000758s ]
---------------------------------------------------------------

[2025-08-03T15:03:45+08:00] ************** POST www.lotteup.com/api/Transaction/createUnifiedOrder
[ info ] BaseController action: createunifiedorder
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 $_POST[token]: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 $_REQUEST[token]: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] 🔍 最终user_token: a1e7naANjvp9kPAGhLxhy8LBpkS9a/1prGnF3Njirtzvu4fpliw2OjI
[ info ] Unified payment request: {"uid":"1149","recharge_id":"118","channel_mode":"jaya_pay","amount":50005,"pay_type":"not_required"}
[ info ] TransactionService createRechargeOrder: {"channel_mode":"jaya_pay","recharge_id":"118","amount":"50005"}
[ info ] JayaPay sign string: 20250803150345user@example.com1440S820250727142109000064Userhttps://www.lotteup.com/api/transaction/unifiedCallbackJP2025080315034517735905000508**********在线充值
[ info ] JayaPay recharge order created: {"productDetail":"在线充值","orderNum":"JP20250803150345177359","platRespCode":"SUCCESS","url":"https://uaw28.glorioushop.com/cash/?orderNum=PT1B168C55D700405A&SG=770dbe376ee52c1e11c4bb95eaebbb7c&SX=0efcce7a14fc5d66e2fcee10a680b9d9&SV=5ac797a2b8702bcbdcd682e90e1b19e4&SN=a091090fbd4e516db87ac0fa36777193","platOrderNum":"PT1B168C55D700405A","payMoney":"50005","name":"User","platSign":"RcgvcJza5cGsTC5JHp5v2nOIhU2n44ea77ZNngjL0i7fwl8NV62gl5upduULXAGJGG3v0/sGV8Hd8IUTC6mqeRRBbwErMJg96TJb4gVi+kU37pZnukteJD46IIP1o/tsbxUExmk0CYD3NJtSPa6wQoWIpDvZThNBg0WCEzmtF8gNsuPBkYtwcDj6VJ7UdoQKy5/v8mbf4QK4lvBngzxmoND234eHs0BQCNEwQZQvBPTdSFIG+9jsM1mvZtrjnemlK/qwflbhiVqzE2bVQOnTy5W6BrlV/cR5ihY93AXo+9GY0McL3EucNRJLeWALk15KgbIwe+NewKXSzdvyh36g0C4Udv/Z1CPaBB408Q3m8Ks85hyenHj6m7I5gfr/ABAgCXsn7mHIYQ9Fflbdwa5hHnTn78BgGdPICYW8iCnnj1/CxAGSmTgsnfqCJjQoL7uXp9GNqATHXOgGu2BHkyGkyNSdRA4nojG7MmwdC4Kpy6/Xb1giVeAaPCa+Cw4NfpGc","platRespMessage":"Request Transaction Success","email":"<EMAIL>"}
[ sql ] [ DB ] CONNECT:[ UseTime:0.000611s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001606s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1149  AND `username` = '1111111'  AND `state` = 1 [ RunTime:0.000769s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000875s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1149 , 1754204625 , '[\"country_code\",\"recharge_id\",\"amount\",\"currency\",\"lang\",\"token\"]' , '[\"ID\",\"118\",\"50005\",\"IDR\",\"cn\",\"a1e7naANjvp9kPAGhLxhy8LBpkS9a\\/1prGnF3Njirtzvu4fpliw2OjI\"]' , '**************' , 'createunifiedorder' , 'Transaction') [ RunTime:0.000599s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_rechange_type` [ RunTime:0.001071s ]
[ sql ] [ SQL ] SELECT * FROM `ly_rechange_type` WHERE  `id` = 118  AND `state` = 1 LIMIT 1 [ RunTime:0.000663s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `id` = 1149 LIMIT 1 [ RunTime:0.000780s ]
[ sql ] [ SQL ] SELECT * FROM `ly_rechange_type` WHERE  `id` = 118  AND `mode` = 'jaya_pay'  AND `state` = 1 LIMIT 1 [ RunTime:0.000524s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001681s ]
[ sql ] [ SQL ] INSERT INTO `ly_user_recharge` (`uid` , `order_number` , `money` , `daozhang_money` , `fee` , `type` , `add_time` , `state` , `remarks`) VALUES (1149 , 'JP20250803150345177359' , 50005 , 50005 , 0 , 118 , 1754204625 , 0 , 'JayaPay充值订单') [ RunTime:0.000800s ]
[ sql ] [ SQL ] UPDATE `ly_user_recharge`  SET `submitUrl` = 'https://uaw28.glorioushop.com/cash/?orderNum=PT1B168C55D700405A&SG=770dbe376ee52c1e11c4bb95eaebbb7c&SX=0efcce7a14fc5d66e2fcee10a680b9d9&SV=5ac797a2b8702bcbdcd682e90e1b19e4&SN=a091090fbd4e516db87ac0fa36777193'  WHERE  `id` = 66 [ RunTime:0.000720s ]
---------------------------------------------------------------

[2025-08-03T15:04:00+08:00] ************** POST www.lotteup.com/api/transaction/unifiedCallback
[ info ] BaseController action: unifiedcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Unified callback received: {"msg":"SUCCESS","code":"00","method":"QRIS","orderNum":"JP20250803150345177359","platOrderNum":"PT1B168C55D700405A","payFee":"2250.23","payMoney":"50005","phone":"08**********","name":"User","platSign":"ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt\/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=","email":"<EMAIL>","status":"SUCCESS"}
[ info ] TransactionService handleCallback received: {"msg":"SUCCESS","code":"00","method":"QRIS","orderNum":"JP20250803150345177359","platOrderNum":"PT1B168C55D700405A","payFee":"2250.23","payMoney":"50005","phone":"08**********","name":"User","platSign":"ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt\/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=","email":"<EMAIL>","status":"SUCCESS"}
[ info ] Detected channel type: jaya_pay
[ info ] JayaPay sign string: 00user@example.comQRISSUCCESSUserJP202508031503451773592250.235000508**********PT1B168C55D700405ASUCCESS
[ error ] JayaPay decrypt sign error: RSA public decrypt failed
[ error ] JayaPay decrypt sign debug - encryptedSign length: 172
[ error ] JayaPay decrypt sign debug - publicKey: -----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQU...
[ error ] JayaPay verify sign error: RSA public decrypt failed
[ error ] JayaPay recharge notify sign verify failed
---------------------------------------------------------------

[2025-08-03T15:04:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000747s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001261s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754204641 LIMIT 100 [ RunTime:0.000494s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.002292s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000550s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000539s ]
---------------------------------------------------------------

[2025-08-03T15:04:02+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000820s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001312s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000830s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001043s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000569s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001313s ]
---------------------------------------------------------------

[2025-08-03T15:04:04+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000623s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001239s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000954s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001207s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000814s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001401s ]
---------------------------------------------------------------

[2025-08-03T15:04:08+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000678s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001021s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000849s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000917s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000708s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001336s ]
---------------------------------------------------------------

[2025-08-03T15:04:10+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000791s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001103s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000771s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000953s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000685s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001273s ]
---------------------------------------------------------------

[2025-08-03T15:04:11+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000626s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001116s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000981s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001088s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000646s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001333s ]
---------------------------------------------------------------

[2025-08-03T15:04:11+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000691s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001208s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000889s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001063s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000611s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001191s ]
---------------------------------------------------------------

[2025-08-03T15:04:11+08:00] ************** POST www.lotteup.com/api/transaction/unifiedCallback
[ info ] BaseController action: unifiedcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Unified callback received: {"msg":"SUCCESS","code":"00","method":"QRIS","orderNum":"JP20250803150345177359","platOrderNum":"PT1B168C55D700405A","payFee":"2250.23","payMoney":"50005","phone":"08**********","name":"User","platSign":"ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt\/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=","email":"<EMAIL>","status":"SUCCESS"}
[ info ] TransactionService handleCallback received: {"msg":"SUCCESS","code":"00","method":"QRIS","orderNum":"JP20250803150345177359","platOrderNum":"PT1B168C55D700405A","payFee":"2250.23","payMoney":"50005","phone":"08**********","name":"User","platSign":"ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt\/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=","email":"<EMAIL>","status":"SUCCESS"}
[ info ] Detected channel type: jaya_pay
[ info ] JayaPay sign string: 00user@example.comQRISSUCCESSUserJP202508031503451773592250.235000508**********PT1B168C55D700405ASUCCESS
[ error ] JayaPay decrypt sign error: RSA public decrypt failed
[ error ] JayaPay decrypt sign debug - encryptedSign length: 172
[ error ] JayaPay decrypt sign debug - publicKey: -----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQU...
[ error ] JayaPay verify sign error: RSA public decrypt failed
[ error ] JayaPay recharge notify sign verify failed
---------------------------------------------------------------

[2025-08-03T15:04:12+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000759s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001169s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.001001s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001177s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000752s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001484s ]
---------------------------------------------------------------

[2025-08-03T15:04:12+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000619s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001113s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000865s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000962s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000684s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001268s ]
---------------------------------------------------------------

[2025-08-03T15:04:12+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000566s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001439s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000815s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001048s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000699s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001325s ]
---------------------------------------------------------------

[2025-08-03T15:04:13+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000652s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001399s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.001039s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000980s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000909s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001608s ]
---------------------------------------------------------------

[2025-08-03T15:04:13+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000736s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001026s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000914s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001290s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000857s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001367s ]
---------------------------------------------------------------

[2025-08-03T15:04:14+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000866s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001282s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.001019s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001196s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000760s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001249s ]
---------------------------------------------------------------

[2025-08-03T15:04:14+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000706s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001389s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.001025s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001278s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000739s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001551s ]
---------------------------------------------------------------

[2025-08-03T15:04:15+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.001043s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001640s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.001229s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001894s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.001082s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.002208s ]
---------------------------------------------------------------

[2025-08-03T15:04:16+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000889s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001520s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.001232s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001327s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000836s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001683s ]
---------------------------------------------------------------

[2025-08-03T15:04:16+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000695s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001227s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.001235s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001384s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000556s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001486s ]
---------------------------------------------------------------

[2025-08-03T15:04:17+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000601s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001234s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000897s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001290s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000716s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001284s ]
---------------------------------------------------------------

[2025-08-03T15:04:17+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000766s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001038s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000834s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000993s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000764s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001319s ]
---------------------------------------------------------------

[2025-08-03T15:04:18+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000917s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001699s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000931s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001214s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000777s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001581s ]
---------------------------------------------------------------

[2025-08-03T15:04:22+08:00] 2401:b60:17::204 GET www.lotteup.com/manage/Bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000719s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001308s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000927s ]
---------------------------------------------------------------

[2025-08-03T15:04:23+08:00] 2401:b60:17::204 GET www.lotteup.com/manage/withdrawal_channel/getEnabledChannels
[ sql ] [ DB ] CONNECT:[ UseTime:0.000664s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001406s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'WithdrawalChannel/getenabledchannels'  AND `state` = 1 [ RunTime:0.000707s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_withdrawal_channel` [ RunTime:0.000904s ]
[ sql ] [ SQL ] SELECT `id`,`name`,`code`,`mode`,`min_amount`,`max_amount`,`fee_rate` FROM `ly_withdrawal_channel` WHERE  `state` = 1 ORDER BY `sort` ASC [ RunTime:0.000511s ]
---------------------------------------------------------------

[2025-08-03T15:04:23+08:00] 2401:b60:17::204 POST www.lotteup.com/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000643s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001134s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000811s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001334s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.001041s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.001753s ]
---------------------------------------------------------------

[2025-08-03T15:04:34+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000849s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.002022s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.001153s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001657s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000775s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001478s ]
---------------------------------------------------------------

[2025-08-03T15:04:35+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000634s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001327s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000845s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001135s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000666s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001173s ]
---------------------------------------------------------------

[2025-08-03T15:04:35+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000801s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001265s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000778s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001038s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000841s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001293s ]
---------------------------------------------------------------

[2025-08-03T15:04:36+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000895s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001200s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000884s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001218s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000716s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001602s ]
---------------------------------------------------------------

[2025-08-03T15:04:42+08:00] ************** POST www.lotteup.com/api/transaction/unifiedCallback
[ info ] BaseController action: unifiedcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Unified callback received: {"msg":"SUCCESS","code":"00","method":"QRIS","orderNum":"JP20250803150345177359","platOrderNum":"PT1B168C55D700405A","payFee":"2250.23","payMoney":"50005","phone":"08**********","name":"User","platSign":"ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt\/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=","email":"<EMAIL>","status":"SUCCESS"}
[ info ] TransactionService handleCallback received: {"msg":"SUCCESS","code":"00","method":"QRIS","orderNum":"JP20250803150345177359","platOrderNum":"PT1B168C55D700405A","payFee":"2250.23","payMoney":"50005","phone":"08**********","name":"User","platSign":"ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt\/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=","email":"<EMAIL>","status":"SUCCESS"}
[ info ] Detected channel type: jaya_pay
[ info ] JayaPay sign string: 00user@example.comQRISSUCCESSUserJP202508031503451773592250.235000508**********PT1B168C55D700405ASUCCESS
[ error ] JayaPay decrypt sign error: RSA public decrypt failed
[ error ] JayaPay decrypt sign debug - encryptedSign length: 172
[ error ] JayaPay decrypt sign debug - publicKey: -----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQU...
[ error ] JayaPay verify sign error: RSA public decrypt failed
[ error ] JayaPay recharge notify sign verify failed
---------------------------------------------------------------

[2025-08-03T15:05:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000826s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001684s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754204701 LIMIT 100 [ RunTime:0.000823s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.002238s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000697s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.001013s ]
---------------------------------------------------------------

[2025-08-03T15:05:29+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000811s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001396s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.001504s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001383s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000764s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001757s ]
---------------------------------------------------------------

[2025-08-03T15:05:30+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000798s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001257s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000980s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001236s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000651s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001273s ]
---------------------------------------------------------------

[2025-08-03T15:05:31+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000633s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001090s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000968s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001038s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000548s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001351s ]
---------------------------------------------------------------

[2025-08-03T15:05:31+08:00] ************** POST lotteup.com/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000766s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001629s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000930s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001232s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000777s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001898s ]
---------------------------------------------------------------

[2025-08-03T15:05:43+08:00] ************** POST www.lotteup.com/api/transaction/unifiedCallback
[ info ] BaseController action: unifiedcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Unified callback received: {"msg":"SUCCESS","code":"00","method":"QRIS","orderNum":"JP20250803150345177359","platOrderNum":"PT1B168C55D700405A","payFee":"2250.23","payMoney":"50005","phone":"08**********","name":"User","platSign":"ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt\/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=","email":"<EMAIL>","status":"SUCCESS"}
[ info ] TransactionService handleCallback received: {"msg":"SUCCESS","code":"00","method":"QRIS","orderNum":"JP20250803150345177359","platOrderNum":"PT1B168C55D700405A","payFee":"2250.23","payMoney":"50005","phone":"08**********","name":"User","platSign":"ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt\/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=","email":"<EMAIL>","status":"SUCCESS"}
[ info ] Detected channel type: jaya_pay
[ info ] JayaPay sign string: 00user@example.comQRISSUCCESSUserJP202508031503451773592250.235000508**********PT1B168C55D700405ASUCCESS
[ error ] JayaPay decrypt sign error: RSA public decrypt failed
[ error ] JayaPay decrypt sign debug - encryptedSign length: 172
[ error ] JayaPay decrypt sign debug - publicKey: -----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQU...
[ error ] JayaPay verify sign error: RSA public decrypt failed
[ error ] JayaPay recharge notify sign verify failed
---------------------------------------------------------------

[2025-08-03T15:06:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000927s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001745s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754204761 LIMIT 100 [ RunTime:0.000937s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.002079s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000544s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000705s ]
---------------------------------------------------------------

[2025-08-03T15:06:01+08:00] ************** POST lotteup.com/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000598s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001175s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000927s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.002068s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.001027s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.001877s ]
---------------------------------------------------------------

[2025-08-03T15:06:10+08:00] ************** POST www.lotteup.com/api/Account/getBankCardList
[ info ] BaseController action: getbankcardlist
[ info ] BaseController controller: Account
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 2170wuKMrgKybZM5Xkl1mwcoV7pE4qv9U5+G/+HKxabxg5rlfd43cQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 2170wuKMrgKybZM5Xkl1mwcoV7pE4qv9U5+G/+HKxabxg5rlfd43cQ
[ info ] 🔍 $_POST[token]: 2170wuKMrgKybZM5Xkl1mwcoV7pE4qv9U5+G/+HKxabxg5rlfd43cQ
[ info ] 🔍 $_REQUEST[token]: 2170wuKMrgKybZM5Xkl1mwcoV7pE4qv9U5+G/+HKxabxg5rlfd43cQ
[ info ] 🔍 最终user_token: 2170wuKMrgKybZM5Xkl1mwcoV7pE4qv9U5+G/+HKxabxg5rlfd43cQ
---------------------------------------------------------------

[2025-08-03T15:06:10+08:00] ************** POST www.lotteup.com/api/Transaction/getRechargeRecord
[ info ] BaseController action: getrechargerecord
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 2170wuKMrgKybZM5Xkl1mwcoV7pE4qv9U5+G/+HKxabxg5rlfd43cQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 2170wuKMrgKybZM5Xkl1mwcoV7pE4qv9U5+G/+HKxabxg5rlfd43cQ
[ info ] 🔍 $_POST[token]: 2170wuKMrgKybZM5Xkl1mwcoV7pE4qv9U5+G/+HKxabxg5rlfd43cQ
[ info ] 🔍 $_REQUEST[token]: 2170wuKMrgKybZM5Xkl1mwcoV7pE4qv9U5+G/+HKxabxg5rlfd43cQ
[ info ] 🔍 最终user_token: 2170wuKMrgKybZM5Xkl1mwcoV7pE4qv9U5+G/+HKxabxg5rlfd43cQ
---------------------------------------------------------------

[2025-08-03T15:06:18+08:00] ************** POST www.lotteup.com/api/User/Login
[ info ] BaseController action: login
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000664s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001533s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `username` = '222222'  AND ( state in(1,2) ) LIMIT 1 [ RunTime:0.000702s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_loginlog` [ RunTime:0.000999s ]
[ sql ] [ SQL ] INSERT INTO `ly_loginlog` (`uid` , `username` , `os` , `browser` , `ip` , `time` , `address` , `type`) VALUES (1150 , '222222' , '未知操作系统' , '未知浏览器' , '**************' , ********** , '' , '前台手机网页版') [ RunTime:0.000441s ]
[ sql ] [ SQL ] UPDATE `ly_users`  SET `last_ip` = '**************' , `last_login` = ********** , `login_error` = 0 , `login_number` = `login_number` + 1  WHERE  `id` = 1150 [ RunTime:0.000697s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_total` [ RunTime:0.001412s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_total` WHERE  `uid` = 1150 LIMIT 1 [ RunTime:0.000562s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001364s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000626s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.001303s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000743s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000472s ]
---------------------------------------------------------------

[2025-08-03T15:06:18+08:00] ************** POST www.lotteup.com/api/Account/getBankCardList
[ info ] BaseController action: getbankcardlist
[ info ] BaseController controller: Account
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 $_POST[token]: 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 $_REQUEST[token]: 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 最终user_token: 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ sql ] [ DB ] CONNECT:[ UseTime:0.000785s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001971s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000676s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.001435s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , ********** , '[\"lang\",\"token\"]' , '[\"cn\",\"067fydGm5thkTr5y9P4NtGv\\/07pFBT+AxJMJ\\/VBHTb75ahrsJZktUA\"]' , '**************' , 'getbankcardlist' , 'Account') [ RunTime:0.000961s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_bank` [ RunTime:0.001438s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_bank` WHERE  `uid` = 1150  AND `status` = 1 ORDER BY `id` DESC [ RunTime:0.000759s ]
---------------------------------------------------------------

[2025-08-03T15:06:18+08:00] ************** POST www.lotteup.com/api/Transaction/getRechargeRecord
[ info ] BaseController action: getrechargerecord
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 $_POST[token]: 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 $_REQUEST[token]: 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 最终user_token: 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ sql ] [ DB ] CONNECT:[ UseTime:0.000788s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.002087s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000564s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.001378s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , ********** , '[\"state\",\"page_no\",\"lang\",\"token\"]' , '[\"0\",\"1\",\"cn\",\"067fydGm5thkTr5y9P4NtGv\\/07pFBT+AxJMJ\\/VBHTb75ahrsJZktUA\"]' , '**************' , 'getrechargerecord' , 'Transaction') [ RunTime:0.000949s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001725s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `uid` = 1150 [ RunTime:0.000654s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_recharge` WHERE  `uid` = 1150 ORDER BY `add_time` DESC,`id` DESC LIMIT 0,10 [ RunTime:0.000733s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_bank` [ RunTime:0.001036s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000503s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_rechange_type` [ RunTime:0.001131s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000415s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000429s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000323s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000351s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000329s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000594s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 118 LIMIT 1 [ RunTime:0.000584s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000620s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000475s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000360s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000406s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000361s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000346s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000350s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000447s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000306s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000494s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000415s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000352s ]
---------------------------------------------------------------

[2025-08-03T15:06:30+08:00] ************** POST www.lotteup.com/api/Transaction/draw
[ info ] BaseController action: draw
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 $_POST[token]: 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 $_REQUEST[token]: 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 最终user_token: 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ sql ] [ DB ] CONNECT:[ UseTime:0.000588s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001473s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000473s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000934s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , ********** , '[\"draw_type\",\"bank\",\"user_bank_id\",\"draw_money\",\"drawword\",\"ifsc\",\"lang\",\"token\"]' , '[\"bank\",\"DANA\",\"341\",\"50000\",\"111111\",\"\",\"cn\",\"067fydGm5thkTr5y9P4NtGv\\/07pFBT+AxJMJ\\/VBHTb75ahrsJZktUA\"]' , '**************' , 'draw' , 'Transaction') [ RunTime:0.000479s ]
[ sql ] [ SQL ] SELECT `user_type` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000409s ]
[ sql ] [ SQL ] SELECT `state` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000361s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `withdrawals_state` = 1 [ RunTime:0.000512s ]
[ sql ] [ SQL ] SELECT `credit` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000389s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001390s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000702s ]
[ sql ] [ SQL ] SELECT `fund_password` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000440s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_total` [ RunTime:0.000986s ]
[ sql ] [ SQL ] SELECT `balance` FROM `ly_user_total` WHERE  `uid` = 1150 LIMIT 1 [ RunTime:0.000443s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_bank` [ RunTime:0.001164s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_bank` WHERE  `id` = 341 LIMIT 1 [ RunTime:0.000518s ]
[ sql ] [ SQL ] SELECT `balance` FROM `ly_user_total` WHERE  `uid` = 1150 LIMIT 1 [ RunTime:0.000431s ]
[ sql ] [ SQL ] UPDATE `ly_user_total`  SET `balance` = `balance` - 55000  WHERE  `uid` = 1150  AND `balance` >= 55000 [ RunTime:0.000484s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001135s ]
[ sql ] [ SQL ] INSERT INTO `ly_user_withdrawals` (`uid` , `price` , `card_name` , `card_number` , `bank_id` , `bank_name` , `time` , `order_number` , `trade_number` , `fee` , `remarks`) VALUES (1150 , '50000' , 'aa' , '*************' , 19 , 'DANA' , ********** , '202508031506301400899836' , '202508031506301442467444' , 5000 , '尊敬的用户您好！您的编号为202508031506301400899836 的提现处理中，金额￥50000元 服务费：￥5000元，处理时间：2025-08-03 15:06:30') [ RunTime:0.000544s ]
[ sql ] [ SQL ] SELECT `vip_level` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000593s ]
[ sql ] [ SQL ] SELECT `realname`,`username`,`vip_level`,`user_type` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000512s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001445s ]
[ sql ] [ SQL ] INSERT INTO `ly_trade_details` (`types` , `username` , `vip_level` , `user_type` , `uid` , `sid` , `source_uid` , `source_username` , `order_number` , `trade_number` , `trade_time` , `isadmin` , `trade_amount` , `trade_before_balance` , `account_balance` , `account_total_balance` , `remarks` , `remarks_en` , `remarks_id` , `remarks_ft` , `remarks_yd` , `remarks_vi` , `remarks_es` , `remarks_ja` , `remarks_th` , `remarks_ma` , `remarks_pt` , `state` , `trade_type`) VALUES (1 , '222222' , 1 , 2 , 1150 , 1150 , 0 , '' , '202508031506301400899836' , '202508031506301442467444' , ********** , 2 , 55000 , '180000.00' , 125000 , 0 , '平台取款(含手续费￥5000)' , 'Platform withdrawal (including fee $5000)' , 'Penarikan platform (termasuk biaya Rp5000)' , '平台取款(含手續費￥5000)' , 'प्लेटफार्म निकासी (शुल्क ₹5000 सहित)' , 'Rút tiền nền tảng (bao gồm phí 5000đ)' , 'Retiro de plataforma (incluyendo tarifa €5000)' , 'プラットフォーム出金(手数料¥5000を含む)' , 'การถอนเงินแพลตฟอร์ม (รวมค่าธรรมเนียม ฿5000)' , 'Pengeluaran platform (termasuk yuran RM5000)' , 'Retirada da plataforma (incluindo taxa R$5000)' , 3 , 2) [ RunTime:0.000839s ]
---------------------------------------------------------------

[2025-08-03T15:06:30+08:00] ************** POST www.lotteup.com/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 $_POST[token]: 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 $_REQUEST[token]: 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 最终user_token: 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ sql ] [ DB ] CONNECT:[ UseTime:0.000894s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001588s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000597s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.001143s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , ********** , '[\"lang\",\"token\"]' , '[\"cn\",\"067fydGm5thkTr5y9P4NtGv\\/07pFBT+AxJMJ\\/VBHTb75ahrsJZktUA\"]' , '**************' , 'getuserinfo' , 'User') [ RunTime:0.000833s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000870s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001268s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000698s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001255s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754150399  AND `state` = 1 [ RunTime:0.000603s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000414s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000397s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000708s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000562s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000436s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.001044s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000367s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000426s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000429s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000969s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000449s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000370s ]
---------------------------------------------------------------

[2025-08-03T15:06:30+08:00] ************** POST www.lotteup.com/api/Transaction/getRechargeRecord
[ info ] BaseController action: getrechargerecord
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 $_POST[token]: 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 $_REQUEST[token]: 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ info ] 🔍 最终user_token: 067fydGm5thkTr5y9P4NtGv/07pFBT+AxJMJ/VBHTb75ahrsJZktUA
[ sql ] [ DB ] CONNECT:[ UseTime:0.000504s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001284s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000400s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000877s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , ********** , '[\"state\",\"page_no\",\"lang\",\"token\"]' , '[\"0\",\"1\",\"cn\",\"067fydGm5thkTr5y9P4NtGv\\/07pFBT+AxJMJ\\/VBHTb75ahrsJZktUA\"]' , '**************' , 'getrechargerecord' , 'Transaction') [ RunTime:0.000483s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000977s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `uid` = 1150 [ RunTime:0.000375s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_recharge` WHERE  `uid` = 1150 ORDER BY `add_time` DESC,`id` DESC LIMIT 0,10 [ RunTime:0.000731s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_bank` [ RunTime:0.000947s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000320s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_rechange_type` [ RunTime:0.000911s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000335s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000339s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000286s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000340s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000247s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000233s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 118 LIMIT 1 [ RunTime:0.000282s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000242s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000322s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000279s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000384s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000416s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000460s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000297s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000420s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000251s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000274s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000280s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000324s ]
---------------------------------------------------------------

[2025-08-03T15:06:33+08:00] ************** POST lotteup.com/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000606s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001416s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.001053s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001288s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.000801s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.001783s ]
---------------------------------------------------------------

[2025-08-03T15:06:38+08:00] ************** POST lotteup.com/manage/bank/batchAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000516s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001121s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/batchaudit'  AND `state` = 1 [ RunTime:0.000836s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000982s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 300  AND `state` = 3 LIMIT 1 [ RunTime:0.000569s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001302s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000559s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001184s ]
[ sql ] [ SQL ] UPDATE `ly_trade_details`  SET `state` = 4 , `remarks` = '批量审核通过，等待支付'  WHERE  `order_number` = '202508031506301400899836' [ RunTime:0.000552s ]
[ sql ] [ SQL ] UPDATE `ly_user_withdrawals`  SET `examine` = 1 , `aid` = 24 , `set_time` = ********** , `state` = 4 , `remarks` = '尊敬的用户您好！您的编号为 202508031506301400899836 的提现申请已审核通过，正在等待支付，金额￥50000.0000元 服务费：￥5000.0000元，处理时间：2025-08-03 15:06:38'  WHERE  `id` = 300 [ RunTime:0.000420s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_actionlog` [ RunTime:0.000869s ]
[ sql ] [ SQL ] INSERT INTO `ly_actionlog` (`username` , `time` , `ip` , `log` , `isadmin`) VALUES ('admin' , ********** , '**************' , '批量审核订单ID:300（订单号：202508031506301400899836，金额：￥50000.0000）。处理状态：批量审核通过，等待支付' , 1) [ RunTime:0.000400s ]
---------------------------------------------------------------

[2025-08-03T15:06:38+08:00] ************** POST lotteup.com/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000550s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000924s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000743s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001127s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.000638s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.001438s ]
---------------------------------------------------------------

[2025-08-03T15:06:44+08:00] ************** POST lotteup.com/manage/bank/batchWithdrawal
[ sql ] [ DB ] CONNECT:[ UseTime:0.000666s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001189s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/batchwithdrawal'  AND `state` = 1 [ RunTime:0.000818s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_withdrawal_channel` [ RunTime:0.001070s ]
[ sql ] [ SQL ] SELECT * FROM `ly_withdrawal_channel` WHERE  `id` = 6  AND `state` = 1 LIMIT 1 [ RunTime:0.000542s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001336s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 300  AND `state` = 4 LIMIT 1 [ RunTime:0.000611s ]
[ sql ] [ SQL ] UPDATE `ly_user_withdrawals`  SET `channel_id` = 6  WHERE  `id` = 300 [ RunTime:0.000491s ]
[ sql ] [ SQL ] SELECT * FROM `ly_withdrawal_channel` WHERE  `id` = 6 LIMIT 1 [ RunTime:0.000513s ]
[ sql ] [ SQL ] UPDATE `ly_user_withdrawals`  SET `state` = 5 , `process_time` = **********  WHERE  `id` = 300 [ RunTime:0.000658s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_actionlog` [ RunTime:0.001065s ]
[ sql ] [ SQL ] INSERT INTO `ly_actionlog` (`username` , `time` , `ip` , `log` , `isadmin`) VALUES ('admin' , ********** , '**************' , '批量代付订单ID:300（订单号：202508031506301400899836，金额：￥50000.0000，渠道：JayaPay代付APP）' , 1) [ RunTime:0.000527s ]
[ info ] 开始批量代付: 订单数量=1, 渠道=JayaPay代付APP(jaya_pay), 操作员=admin
[ info ] 批量代付处理订单: ID=300, 订单号=202508031506301400899836, 金额=50000.0000, 渠道=JayaPay代付APP(jaya_pay)
[ info ] JayaPay sign string: 014IDR20250803150643代付下单user@example.com1S820250727142109000064Transfer08**********50000aahttps://www.lotteup.com/api/transaction/unifiedWithdrawalCallback*************2025080315063014008998360
[ info ] JayaPay withdrawal direct success: {"bankCode":"014","fee":"7000","orderNum":"202508031506301400899836","description":"代付下单","platRespCode":"SUCCESS","feeType":"1","platOrderNum":"YBL1951902536864641079","number":"*************","money":"50000","statusMsg":"Apply","name":"aa","platSign":"fUSfwhQNRc47JDunjuzf6Ph9qmlUOriqfWaxmcD4BSbn7whAS4aQMmR4Cl9Zn7Njq/aLu1rdU2noqTMAjjAjYWy1CoN3ggaowdVw+yCNm/9Czpx6ZeS+rlbxlCbcFVG/ZgPOD88j+wnht3Kea4eqALzbpPQFZyxUW0kZORyYCk8=","platRespMessage":"Request success","status":0}
[ info ] 批量代付成功: 订单ID=300, 订单号=202508031506301400899836, 响应=代付请求已提交
[ info ] 批量代付完成: 渠道=JayaPay代付APP(jaya_pay), 成功=1条, 失败=0条, 操作员=admin
---------------------------------------------------------------

[2025-08-03T15:06:44+08:00] ************** POST lotteup.com/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000568s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001276s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000722s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001358s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.000651s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.001210s ]
---------------------------------------------------------------

[2025-08-03T15:07:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000902s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001843s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= ********** LIMIT 100 [ RunTime:0.000797s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001814s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000468s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000500s ]
---------------------------------------------------------------

[2025-08-03T15:07:43+08:00] ************** POST www.lotteup.com/api/transaction/unifiedCallback
[ info ] BaseController action: unifiedcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Unified callback received: {"msg":"SUCCESS","code":"00","method":"QRIS","orderNum":"JP20250803150345177359","platOrderNum":"PT1B168C55D700405A","payFee":"2250.23","payMoney":"50005","phone":"08**********","name":"User","platSign":"ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt\/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=","email":"<EMAIL>","status":"SUCCESS"}
[ info ] TransactionService handleCallback received: {"msg":"SUCCESS","code":"00","method":"QRIS","orderNum":"JP20250803150345177359","platOrderNum":"PT1B168C55D700405A","payFee":"2250.23","payMoney":"50005","phone":"08**********","name":"User","platSign":"ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt\/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=","email":"<EMAIL>","status":"SUCCESS"}
[ info ] Detected channel type: jaya_pay
[ info ] JayaPay sign string: 00user@example.comQRISSUCCESSUserJP202508031503451773592250.235000508**********PT1B168C55D700405ASUCCESS
[ error ] JayaPay decrypt sign error: RSA public decrypt failed
[ error ] JayaPay decrypt sign debug - encryptedSign length: 172
[ error ] JayaPay decrypt sign debug - publicKey: -----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQU...
[ error ] JayaPay verify sign error: RSA public decrypt failed
[ error ] JayaPay recharge notify sign verify failed
---------------------------------------------------------------

[2025-08-03T15:08:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000856s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001517s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754204881 LIMIT 100 [ RunTime:0.000565s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001807s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000405s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000503s ]
---------------------------------------------------------------

[2025-08-03T15:09:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000970s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001657s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754204941 LIMIT 100 [ RunTime:0.000763s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.002089s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000588s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000989s ]
---------------------------------------------------------------

[2025-08-03T15:09:52+08:00] ************** POST www.lotteup.com/api/transaction/unifiedWithdrawalCallback
[ info ] BaseController action: unifiedwithdrawalcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Current action: unifiedwithdrawalcallback
[ info ] Unified withdrawal callback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031211500962316354","feeType":"1","number":"*************","platOrderNum":"YBL1951858600607498252","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"K0HLRCzhz\/5NT561l511TTBDd+zjmqgffJ8M6gJonMYDkw8sybud8jYCleGesQos+IoqQJag+Y7+o+A+I1\/fCNvKNDc1oum0qBTz\/uC0ViC4uKzX38z1+nWUt3XY9GFMTbvwhxGUZw2cKjIa\/lBAiPiD65glVHKtalZ2CdW+eE8=","status":"4"}
[ info ] TransactionService handleWithdrawalCallback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031211500962316354","feeType":"1","number":"*************","platOrderNum":"YBL1951858600607498252","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"K0HLRCzhz\/5NT561l511TTBDd+zjmqgffJ8M6gJonMYDkw8sybud8jYCleGesQos+IoqQJag+Y7+o+A+I1\/fCNvKNDc1oum0qBTz\/uC0ViC4uKzX38z1+nWUt3XY9GFMTbvwhxGUZw2cKjIa\/lBAiPiD65glVHKtalZ2CdW+eE8=","status":"4"}
[ info ] Detected withdrawal channel type: jaya_pay
[ info ] JayaPay sign string: 014代付下单7000150000aa*************202508031211500962316354YBL19518586006074982524FAILED
[ error ] JayaPay decrypt sign error: RSA public decrypt failed
[ error ] JayaPay decrypt sign debug - encryptedSign length: 172
[ error ] JayaPay decrypt sign debug - publicKey: -----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQU...
[ error ] JayaPay verify sign error: RSA public decrypt failed
[ error ] JayaPay withdrawal notify sign verify failed
---------------------------------------------------------------

[2025-08-03T15:09:53+08:00] ************** POST www.lotteup.com/api/transaction/unifiedWithdrawalCallback
[ info ] BaseController action: unifiedwithdrawalcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Current action: unifiedwithdrawalcallback
[ info ] Unified withdrawal callback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031506301400899836","feeType":"1","number":"*************","platOrderNum":"YBL1951902536864641079","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"JI7wpyqiMryARy6GYHungd9XsMqg4GVvXSP7gG2aCxG6L5MK\/fBlmm5DPaSkamFRqEBYDIWfkf+1rpKUwuCxyA2w6tg8KLHmjbIgz28c9u6G4eIx3cY3aJ3D0M2p4xxIKSz9mnycKKLO\/kd+\/fJycZdhAaxL4vRmEleXn1ApTH8=","status":"4"}
[ info ] TransactionService handleWithdrawalCallback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031506301400899836","feeType":"1","number":"*************","platOrderNum":"YBL1951902536864641079","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"JI7wpyqiMryARy6GYHungd9XsMqg4GVvXSP7gG2aCxG6L5MK\/fBlmm5DPaSkamFRqEBYDIWfkf+1rpKUwuCxyA2w6tg8KLHmjbIgz28c9u6G4eIx3cY3aJ3D0M2p4xxIKSz9mnycKKLO\/kd+\/fJycZdhAaxL4vRmEleXn1ApTH8=","status":"4"}
[ info ] Detected withdrawal channel type: jaya_pay
[ info ] JayaPay sign string: 014代付下单7000150000aa*************202508031506301400899836YBL19519025368646410794FAILED
[ error ] JayaPay decrypt sign error: RSA public decrypt failed
[ error ] JayaPay decrypt sign debug - encryptedSign length: 172
[ error ] JayaPay decrypt sign debug - publicKey: -----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQU...
[ error ] JayaPay verify sign error: RSA public decrypt failed
[ error ] JayaPay withdrawal notify sign verify failed
---------------------------------------------------------------

[2025-08-03T15:10:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000811s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001338s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754205001 LIMIT 100 [ RunTime:0.000498s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001569s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000576s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000519s ]
---------------------------------------------------------------

[2025-08-03T15:10:03+08:00] ************** POST www.lotteup.com/api/transaction/unifiedWithdrawalCallback
[ info ] BaseController action: unifiedwithdrawalcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Current action: unifiedwithdrawalcallback
[ info ] Unified withdrawal callback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031211500962316354","feeType":"1","number":"*************","platOrderNum":"YBL1951858600607498252","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"K0HLRCzhz\/5NT561l511TTBDd+zjmqgffJ8M6gJonMYDkw8sybud8jYCleGesQos+IoqQJag+Y7+o+A+I1\/fCNvKNDc1oum0qBTz\/uC0ViC4uKzX38z1+nWUt3XY9GFMTbvwhxGUZw2cKjIa\/lBAiPiD65glVHKtalZ2CdW+eE8=","status":"4"}
[ info ] TransactionService handleWithdrawalCallback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031211500962316354","feeType":"1","number":"*************","platOrderNum":"YBL1951858600607498252","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"K0HLRCzhz\/5NT561l511TTBDd+zjmqgffJ8M6gJonMYDkw8sybud8jYCleGesQos+IoqQJag+Y7+o+A+I1\/fCNvKNDc1oum0qBTz\/uC0ViC4uKzX38z1+nWUt3XY9GFMTbvwhxGUZw2cKjIa\/lBAiPiD65glVHKtalZ2CdW+eE8=","status":"4"}
[ info ] Detected withdrawal channel type: jaya_pay
[ info ] JayaPay sign string: 014代付下单7000150000aa*************202508031211500962316354YBL19518586006074982524FAILED
[ error ] JayaPay decrypt sign error: RSA public decrypt failed
[ error ] JayaPay decrypt sign debug - encryptedSign length: 172
[ error ] JayaPay decrypt sign debug - publicKey: -----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQU...
[ error ] JayaPay verify sign error: RSA public decrypt failed
[ error ] JayaPay withdrawal notify sign verify failed
---------------------------------------------------------------

[2025-08-03T15:10:03+08:00] ************** POST www.lotteup.com/api/transaction/unifiedWithdrawalCallback
[ info ] BaseController action: unifiedwithdrawalcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Current action: unifiedwithdrawalcallback
[ info ] Unified withdrawal callback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031506301400899836","feeType":"1","number":"*************","platOrderNum":"YBL1951902536864641079","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"JI7wpyqiMryARy6GYHungd9XsMqg4GVvXSP7gG2aCxG6L5MK\/fBlmm5DPaSkamFRqEBYDIWfkf+1rpKUwuCxyA2w6tg8KLHmjbIgz28c9u6G4eIx3cY3aJ3D0M2p4xxIKSz9mnycKKLO\/kd+\/fJycZdhAaxL4vRmEleXn1ApTH8=","status":"4"}
[ info ] TransactionService handleWithdrawalCallback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031506301400899836","feeType":"1","number":"*************","platOrderNum":"YBL1951902536864641079","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"JI7wpyqiMryARy6GYHungd9XsMqg4GVvXSP7gG2aCxG6L5MK\/fBlmm5DPaSkamFRqEBYDIWfkf+1rpKUwuCxyA2w6tg8KLHmjbIgz28c9u6G4eIx3cY3aJ3D0M2p4xxIKSz9mnycKKLO\/kd+\/fJycZdhAaxL4vRmEleXn1ApTH8=","status":"4"}
[ info ] Detected withdrawal channel type: jaya_pay
[ info ] JayaPay sign string: 014代付下单7000150000aa*************202508031506301400899836YBL19519025368646410794FAILED
[ error ] JayaPay decrypt sign error: RSA public decrypt failed
[ error ] JayaPay decrypt sign debug - encryptedSign length: 172
[ error ] JayaPay decrypt sign debug - publicKey: -----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQU...
[ error ] JayaPay verify sign error: RSA public decrypt failed
[ error ] JayaPay withdrawal notify sign verify failed
---------------------------------------------------------------

[2025-08-03T15:10:34+08:00] ************** POST www.lotteup.com/api/transaction/unifiedWithdrawalCallback
[ info ] BaseController action: unifiedwithdrawalcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Current action: unifiedwithdrawalcallback
[ info ] Unified withdrawal callback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031506301400899836","feeType":"1","number":"*************","platOrderNum":"YBL1951902536864641079","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"JI7wpyqiMryARy6GYHungd9XsMqg4GVvXSP7gG2aCxG6L5MK\/fBlmm5DPaSkamFRqEBYDIWfkf+1rpKUwuCxyA2w6tg8KLHmjbIgz28c9u6G4eIx3cY3aJ3D0M2p4xxIKSz9mnycKKLO\/kd+\/fJycZdhAaxL4vRmEleXn1ApTH8=","status":"4"}
[ info ] TransactionService handleWithdrawalCallback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031506301400899836","feeType":"1","number":"*************","platOrderNum":"YBL1951902536864641079","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"JI7wpyqiMryARy6GYHungd9XsMqg4GVvXSP7gG2aCxG6L5MK\/fBlmm5DPaSkamFRqEBYDIWfkf+1rpKUwuCxyA2w6tg8KLHmjbIgz28c9u6G4eIx3cY3aJ3D0M2p4xxIKSz9mnycKKLO\/kd+\/fJycZdhAaxL4vRmEleXn1ApTH8=","status":"4"}
[ info ] Detected withdrawal channel type: jaya_pay
[ info ] JayaPay sign string: 014代付下单7000150000aa*************202508031506301400899836YBL19519025368646410794FAILED
[ error ] JayaPay decrypt sign error: RSA public decrypt failed
[ error ] JayaPay decrypt sign debug - encryptedSign length: 172
[ error ] JayaPay decrypt sign debug - publicKey: -----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQU...
[ error ] JayaPay verify sign error: RSA public decrypt failed
[ error ] JayaPay withdrawal notify sign verify failed
---------------------------------------------------------------

[2025-08-03T15:10:34+08:00] ************** POST www.lotteup.com/api/transaction/unifiedWithdrawalCallback
[ info ] BaseController action: unifiedwithdrawalcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Current action: unifiedwithdrawalcallback
[ info ] Unified withdrawal callback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031211500962316354","feeType":"1","number":"*************","platOrderNum":"YBL1951858600607498252","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"K0HLRCzhz\/5NT561l511TTBDd+zjmqgffJ8M6gJonMYDkw8sybud8jYCleGesQos+IoqQJag+Y7+o+A+I1\/fCNvKNDc1oum0qBTz\/uC0ViC4uKzX38z1+nWUt3XY9GFMTbvwhxGUZw2cKjIa\/lBAiPiD65glVHKtalZ2CdW+eE8=","status":"4"}
[ info ] TransactionService handleWithdrawalCallback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031211500962316354","feeType":"1","number":"*************","platOrderNum":"YBL1951858600607498252","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"K0HLRCzhz\/5NT561l511TTBDd+zjmqgffJ8M6gJonMYDkw8sybud8jYCleGesQos+IoqQJag+Y7+o+A+I1\/fCNvKNDc1oum0qBTz\/uC0ViC4uKzX38z1+nWUt3XY9GFMTbvwhxGUZw2cKjIa\/lBAiPiD65glVHKtalZ2CdW+eE8=","status":"4"}
[ info ] Detected withdrawal channel type: jaya_pay
[ info ] JayaPay sign string: 014代付下单7000150000aa*************202508031211500962316354YBL19518586006074982524FAILED
[ error ] JayaPay decrypt sign error: RSA public decrypt failed
[ error ] JayaPay decrypt sign debug - encryptedSign length: 172
[ error ] JayaPay decrypt sign debug - publicKey: -----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQU...
[ error ] JayaPay verify sign error: RSA public decrypt failed
[ error ] JayaPay withdrawal notify sign verify failed
---------------------------------------------------------------

[2025-08-03T15:11:02+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000902s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001192s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754205061 LIMIT 100 [ RunTime:0.000630s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001713s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000558s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000830s ]
---------------------------------------------------------------

[2025-08-03T15:11:34+08:00] ************** POST www.lotteup.com/api/transaction/unifiedWithdrawalCallback
[ info ] BaseController action: unifiedwithdrawalcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Current action: unifiedwithdrawalcallback
[ info ] Unified withdrawal callback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031506301400899836","feeType":"1","number":"*************","platOrderNum":"YBL1951902536864641079","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"JI7wpyqiMryARy6GYHungd9XsMqg4GVvXSP7gG2aCxG6L5MK\/fBlmm5DPaSkamFRqEBYDIWfkf+1rpKUwuCxyA2w6tg8KLHmjbIgz28c9u6G4eIx3cY3aJ3D0M2p4xxIKSz9mnycKKLO\/kd+\/fJycZdhAaxL4vRmEleXn1ApTH8=","status":"4"}
[ info ] TransactionService handleWithdrawalCallback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031506301400899836","feeType":"1","number":"*************","platOrderNum":"YBL1951902536864641079","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"JI7wpyqiMryARy6GYHungd9XsMqg4GVvXSP7gG2aCxG6L5MK\/fBlmm5DPaSkamFRqEBYDIWfkf+1rpKUwuCxyA2w6tg8KLHmjbIgz28c9u6G4eIx3cY3aJ3D0M2p4xxIKSz9mnycKKLO\/kd+\/fJycZdhAaxL4vRmEleXn1ApTH8=","status":"4"}
[ info ] Detected withdrawal channel type: jaya_pay
[ info ] JayaPay sign string: 014代付下单7000150000aa*************202508031506301400899836YBL19519025368646410794FAILED
[ error ] JayaPay decrypt sign error: RSA public decrypt failed
[ error ] JayaPay decrypt sign debug - encryptedSign length: 172
[ error ] JayaPay decrypt sign debug - publicKey: -----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQU...
[ error ] JayaPay verify sign error: RSA public decrypt failed
[ error ] JayaPay withdrawal notify sign verify failed
---------------------------------------------------------------

[2025-08-03T15:11:35+08:00] ************** POST www.lotteup.com/api/transaction/unifiedWithdrawalCallback
[ info ] BaseController action: unifiedwithdrawalcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Current action: unifiedwithdrawalcallback
[ info ] Unified withdrawal callback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031211500962316354","feeType":"1","number":"*************","platOrderNum":"YBL1951858600607498252","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"K0HLRCzhz\/5NT561l511TTBDd+zjmqgffJ8M6gJonMYDkw8sybud8jYCleGesQos+IoqQJag+Y7+o+A+I1\/fCNvKNDc1oum0qBTz\/uC0ViC4uKzX38z1+nWUt3XY9GFMTbvwhxGUZw2cKjIa\/lBAiPiD65glVHKtalZ2CdW+eE8=","status":"4"}
[ info ] TransactionService handleWithdrawalCallback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031211500962316354","feeType":"1","number":"*************","platOrderNum":"YBL1951858600607498252","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"K0HLRCzhz\/5NT561l511TTBDd+zjmqgffJ8M6gJonMYDkw8sybud8jYCleGesQos+IoqQJag+Y7+o+A+I1\/fCNvKNDc1oum0qBTz\/uC0ViC4uKzX38z1+nWUt3XY9GFMTbvwhxGUZw2cKjIa\/lBAiPiD65glVHKtalZ2CdW+eE8=","status":"4"}
[ info ] Detected withdrawal channel type: jaya_pay
[ info ] JayaPay sign string: 014代付下单7000150000aa*************202508031211500962316354YBL19518586006074982524FAILED
[ error ] JayaPay decrypt sign error: RSA public decrypt failed
[ error ] JayaPay decrypt sign debug - encryptedSign length: 172
[ error ] JayaPay decrypt sign debug - publicKey: -----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQU...
[ error ] JayaPay verify sign error: RSA public decrypt failed
[ error ] JayaPay withdrawal notify sign verify failed
---------------------------------------------------------------

[2025-08-03T15:12:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001106s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001512s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754205121 LIMIT 100 [ RunTime:0.000547s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001476s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000492s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000522s ]
---------------------------------------------------------------

[2025-08-03T15:12:02+08:00] 2401:b60:17::204 POST www.lotteup.com/manage/bank/batchWithdrawal
[ sql ] [ DB ] CONNECT:[ UseTime:0.005863s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001454s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/batchwithdrawal'  AND `state` = 1 [ RunTime:0.001628s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_withdrawal_channel` [ RunTime:0.001776s ]
[ sql ] [ SQL ] SELECT * FROM `ly_withdrawal_channel` WHERE  `id` = 7  AND `state` = 1 LIMIT 1 [ RunTime:0.000898s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001707s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 299  AND `state` = 4 LIMIT 1 [ RunTime:0.000795s ]
[ sql ] [ SQL ] UPDATE `ly_user_withdrawals`  SET `channel_id` = 7  WHERE  `id` = 299 [ RunTime:0.000726s ]
[ sql ] [ SQL ] SELECT * FROM `ly_withdrawal_channel` WHERE  `id` = 7 LIMIT 1 [ RunTime:0.000554s ]
[ sql ] [ SQL ] UPDATE `ly_user_withdrawals`  SET `remarks` = '[2025-08-03 15:12:02] watchPay代付失败: BALANCE_NOT_ENOUGH' , `process_time` = **********  WHERE  `id` = 299 [ RunTime:0.001052s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_actionlog` [ RunTime:0.001659s ]
[ sql ] [ SQL ] INSERT INTO `ly_actionlog` (`username` , `time` , `ip` , `log` , `isadmin`) VALUES ('admin' , ********** , '0.0.0.0' , '批量代付失败-订单ID:299（订单号：202508031459301220192220，错误：BALANCE_NOT_ENOUGH）' , 0) [ RunTime:0.000841s ]
[ info ] 开始批量代付: 订单数量=1, 渠道=watchPay(watchPay), 操作员=admin
[ info ] 批量代付处理订单: ID=299, 订单号=202508031459301220192220, 金额=50000.0000, 渠道=watchPay(watchPay)
[ info ] WatchPay config loaded from file: {"enabled":true,"default_gateway":"https:\/\/api.watchglb.com","api_urls":{"pay":"https:\/\/api.watchglb.com\/pay\/web","transfer":"https:\/\/api.watchglb.com\/pay\/transfer","query_transfer":"https:\/\/api.watchglb.com\/query\/transfer","query_balance":"https:\/\/api.watchglb.com\/query\/balance"},"countries":{"ID":{"name":"\u5370\u5c3c","name_en":"Indonesia","currency":"IDR","merchant_id":"*********","pay_key":"YINGPTEMYQ7BAKOSHCAYZESK1WKU8XMK","withdrawal_key":"DLVPXKVOMNOFVEN2AGEQWQUASPN2EPWQ","gateway_url":"https:\/\/api.watchglb.com","notify_domain":"https:\/\/www.lotteup.com","min_amount":20000,"max_amount":********,"enabled":true,"pay_types":{"220":{"name":"\u7f51\u94f6","type":"online","enabled":true,"fee_rate":0.045,"requires_bank_code":true,"min_amount":10000,"max_amount":********},"223":{"name":"\u626b\u7801","type":"scan","enabled":true,"fee_rate":0.055,"requires_bank_code":false,"min_amount":10000,"max_amount":********},"200":{"name":"\u7f51\u94f6B2C\u4e00\u7c7b","type":"online","enabled":false,"requires_bank_code":true},"201":{"name":"\u4fbf\u5229\u5e97\u4e00\u7c7b","type":"offline","enabled":false,"requires_bank_code":false},"202":{"name":"OVO\u94b1\u5305\u4e00\u7c7b","type":"wallet","enabled":false,"requires_bank_code":false},"203":{"name":"QRIS\u626b\u7801\u4e00\u7c7b","type":"scan","enabled":false,"requires_bank_code":false},"240":{"name":"\u7f51\u94f6B2C\u4e09\u7c7b","type":"online","enabled":false,"requires_bank_code":true},"243":{"name":"QRIS\u626b\u7801\u4e09\u7c7b","type":"scan","enabled":false,"requires_bank_code":false}},"supported_banks":{"BCA":{"name":"Bank Central Asia","name_id":"Bank Central Asia","enabled":true},"MANDIRI":{"name":"Bank Mandiri","name_id":"Bank Mandiri","enabled":true},"BNI":{"name":"Bank Negara Indonesia","name_id":"Bank Negara Indonesia","enabled":true},"BRI":{"name":"Bank Rakyat Indonesia","name_id":"Bank Rakyat Indonesia","enabled":true},"PERMATA":{"name":"Bank Permata","name_id":"Bank Permata","enabled":true},"CIMB":{"name":"Bank CIMB Niaga","name_id":"Bank CIMB Niaga","enabled":true},"MAYBANK":{"name":"Bank Maybank","name_id":"Bank Maybank","enabled":true},"DANAMON":{"name":"Bank Danamon","name_id":"Bank Danamon","enabled":true},"BSI":{"name":"Bank Syariah Indonesia","name_id":"Bank Syariah Indonesia","enabled":true},"BNC":{"name":"Neo Commerce","name_id":"Bank Yudha Bhakti","enabled":true}}}},"notify_ips":["*************"]}
[ info ] Found bank code for bank_id 19: DANA -> DANA
[ info ] WatchPay sign debug - signStr: apply_date=2025-08-03 15:12:01&back_url=https://www.lotteup.com/api/transaction/unifiedWithdrawalCallback&bank_code=DANA&mch_id=*********&mch_transferId=202508031459301220192220&receive_account=*************&receive_name=aa&transfer_amount=50000&key=DLVPXKVOMNOFVEN2AGEQWQUASPN2EPWQ
[ info ] WatchPay sign debug - generated sign: e3f7eb136697ecbe258c711710c70075
[ info ] 批量代付完成: 渠道=watchPay(watchPay), 成功=0条, 失败=1条, 操作员=admin
[ error ] WatchPay withdrawal direct failed: {"signType":null,"sign":null,"respCode":"FAIL","mchId":null,"merTransferId":null,"transferAmount":null,"applyDate":null,"tradeNo":null,"tradeResult":null,"errorMsg":"BALANCE_NOT_ENOUGH"}
[ error ] 批量代付失败: 订单ID=299, 订单号=202508031459301220192220, 错误原因=BALANCE_NOT_ENOUGH
---------------------------------------------------------------

[2025-08-03T15:12:02+08:00] 2401:b60:17::204 POST www.lotteup.com/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.001016s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001241s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.001302s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001459s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.000914s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.001771s ]
---------------------------------------------------------------

[2025-08-03T15:12:09+08:00] 2401:b60:17::204 GET www.lotteup.com/manage/bank/withdrawalsDetails?id=300
[ sql ] [ DB ] CONNECT:[ UseTime:0.001027s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001687s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/withdrawalsdetails'  AND `state` = 1 [ RunTime:0.001074s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001176s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 300 LIMIT 1 [ RunTime:0.000774s ]
---------------------------------------------------------------

[2025-08-03T15:12:14+08:00] 2401:b60:17::204 GET www.lotteup.com/manage/bank/withdrawalsDetails?id=299
[ sql ] [ DB ] CONNECT:[ UseTime:0.002573s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001244s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/withdrawalsdetails'  AND `state` = 1 [ RunTime:0.001127s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001557s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 299 LIMIT 1 [ RunTime:0.000709s ]
---------------------------------------------------------------

[2025-08-03T15:12:28+08:00] 2401:b60:17::204 POST www.lotteup.com/manage/bank/batchWithdrawal
[ sql ] [ DB ] CONNECT:[ UseTime:0.000743s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001114s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/batchwithdrawal'  AND `state` = 1 [ RunTime:0.000862s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_withdrawal_channel` [ RunTime:0.001052s ]
[ sql ] [ SQL ] SELECT * FROM `ly_withdrawal_channel` WHERE  `id` = 7  AND `state` = 1 LIMIT 1 [ RunTime:0.000449s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001234s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 299  AND `state` = 4 LIMIT 1 [ RunTime:0.000566s ]
[ sql ] [ SQL ] UPDATE `ly_user_withdrawals`  SET `channel_id` = 7  WHERE  `id` = 299 [ RunTime:0.000551s ]
[ sql ] [ SQL ] SELECT * FROM `ly_withdrawal_channel` WHERE  `id` = 7 LIMIT 1 [ RunTime:0.000374s ]
[ sql ] [ SQL ] UPDATE `ly_user_withdrawals`  SET `remarks` = '[2025-08-03 15:12:28] watchPay代付失败: BALANCE_NOT_ENOUGH' , `process_time` = **********  WHERE  `id` = 299 [ RunTime:0.000652s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_actionlog` [ RunTime:0.001112s ]
[ sql ] [ SQL ] INSERT INTO `ly_actionlog` (`username` , `time` , `ip` , `log` , `isadmin`) VALUES ('admin' , ********** , '0.0.0.0' , '批量代付失败-订单ID:299（订单号：202508031459301220192220，错误：BALANCE_NOT_ENOUGH）' , 0) [ RunTime:0.000609s ]
[ info ] 开始批量代付: 订单数量=1, 渠道=watchPay(watchPay), 操作员=admin
[ info ] 批量代付处理订单: ID=299, 订单号=202508031459301220192220, 金额=50000.0000, 渠道=watchPay(watchPay)
[ info ] WatchPay config loaded from file: {"enabled":true,"default_gateway":"https:\/\/api.watchglb.com","api_urls":{"pay":"https:\/\/api.watchglb.com\/pay\/web","transfer":"https:\/\/api.watchglb.com\/pay\/transfer","query_transfer":"https:\/\/api.watchglb.com\/query\/transfer","query_balance":"https:\/\/api.watchglb.com\/query\/balance"},"countries":{"ID":{"name":"\u5370\u5c3c","name_en":"Indonesia","currency":"IDR","merchant_id":"*********","pay_key":"YINGPTEMYQ7BAKOSHCAYZESK1WKU8XMK","withdrawal_key":"DLVPXKVOMNOFVEN2AGEQWQUASPN2EPWQ","gateway_url":"https:\/\/api.watchglb.com","notify_domain":"https:\/\/www.lotteup.com","min_amount":20000,"max_amount":********,"enabled":true,"pay_types":{"220":{"name":"\u7f51\u94f6","type":"online","enabled":true,"fee_rate":0.045,"requires_bank_code":true,"min_amount":10000,"max_amount":********},"223":{"name":"\u626b\u7801","type":"scan","enabled":true,"fee_rate":0.055,"requires_bank_code":false,"min_amount":10000,"max_amount":********},"200":{"name":"\u7f51\u94f6B2C\u4e00\u7c7b","type":"online","enabled":false,"requires_bank_code":true},"201":{"name":"\u4fbf\u5229\u5e97\u4e00\u7c7b","type":"offline","enabled":false,"requires_bank_code":false},"202":{"name":"OVO\u94b1\u5305\u4e00\u7c7b","type":"wallet","enabled":false,"requires_bank_code":false},"203":{"name":"QRIS\u626b\u7801\u4e00\u7c7b","type":"scan","enabled":false,"requires_bank_code":false},"240":{"name":"\u7f51\u94f6B2C\u4e09\u7c7b","type":"online","enabled":false,"requires_bank_code":true},"243":{"name":"QRIS\u626b\u7801\u4e09\u7c7b","type":"scan","enabled":false,"requires_bank_code":false}},"supported_banks":{"BCA":{"name":"Bank Central Asia","name_id":"Bank Central Asia","enabled":true},"MANDIRI":{"name":"Bank Mandiri","name_id":"Bank Mandiri","enabled":true},"BNI":{"name":"Bank Negara Indonesia","name_id":"Bank Negara Indonesia","enabled":true},"BRI":{"name":"Bank Rakyat Indonesia","name_id":"Bank Rakyat Indonesia","enabled":true},"PERMATA":{"name":"Bank Permata","name_id":"Bank Permata","enabled":true},"CIMB":{"name":"Bank CIMB Niaga","name_id":"Bank CIMB Niaga","enabled":true},"MAYBANK":{"name":"Bank Maybank","name_id":"Bank Maybank","enabled":true},"DANAMON":{"name":"Bank Danamon","name_id":"Bank Danamon","enabled":true},"BSI":{"name":"Bank Syariah Indonesia","name_id":"Bank Syariah Indonesia","enabled":true},"BNC":{"name":"Neo Commerce","name_id":"Bank Yudha Bhakti","enabled":true}}}},"notify_ips":["*************"]}
[ info ] Found bank code for bank_id 19: DANA -> DANA
[ info ] WatchPay sign debug - signStr: apply_date=2025-08-03 15:12:28&back_url=https://www.lotteup.com/api/transaction/unifiedWithdrawalCallback&bank_code=DANA&mch_id=*********&mch_transferId=202508031459301220192220&receive_account=*************&receive_name=aa&transfer_amount=50000&key=DLVPXKVOMNOFVEN2AGEQWQUASPN2EPWQ
[ info ] WatchPay sign debug - generated sign: c96cb2146de813851cbf86a0c70621e2
[ info ] 批量代付完成: 渠道=watchPay(watchPay), 成功=0条, 失败=1条, 操作员=admin
[ error ] WatchPay withdrawal direct failed: {"signType":null,"sign":null,"respCode":"FAIL","mchId":null,"merTransferId":null,"transferAmount":null,"applyDate":null,"tradeNo":null,"tradeResult":null,"errorMsg":"BALANCE_NOT_ENOUGH"}
[ error ] 批量代付失败: 订单ID=299, 订单号=202508031459301220192220, 错误原因=BALANCE_NOT_ENOUGH
---------------------------------------------------------------

[2025-08-03T15:12:28+08:00] 2401:b60:17::204 POST www.lotteup.com/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000818s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001132s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000783s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001077s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.000903s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.001426s ]
---------------------------------------------------------------

[2025-08-03T15:12:32+08:00] 2401:b60:17::204 GET www.lotteup.com/manage/bank/withdrawalsDetails?id=299
[ sql ] [ DB ] CONNECT:[ UseTime:0.000977s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001379s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/withdrawalsdetails'  AND `state` = 1 [ RunTime:0.001006s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001035s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 299 LIMIT 1 [ RunTime:0.000585s ]
---------------------------------------------------------------

[2025-08-03T15:12:36+08:00] 2401:b60:17::204 GET www.lotteup.com/manage/bank/controlAudit?id=299
[ sql ] [ DB ] CONNECT:[ UseTime:0.000762s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001419s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/controlaudit'  AND `state` = 1 [ RunTime:0.001036s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001210s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 299 LIMIT 1 [ RunTime:0.000882s ]
---------------------------------------------------------------

[2025-08-03T15:12:36+08:00] 2401:b60:17::204 GET www.lotteup.com/manage/withdrawal_channel/getEnabledChannels
[ sql ] [ DB ] CONNECT:[ UseTime:0.001013s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001314s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'WithdrawalChannel/getenabledchannels'  AND `state` = 1 [ RunTime:0.001506s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_withdrawal_channel` [ RunTime:0.001281s ]
[ sql ] [ SQL ] SELECT `id`,`name`,`code`,`mode`,`min_amount`,`max_amount`,`fee_rate` FROM `ly_withdrawal_channel` WHERE  `state` = 1 ORDER BY `sort` ASC [ RunTime:0.000535s ]
---------------------------------------------------------------

[2025-08-03T15:12:43+08:00] 2401:b60:17::204 POST www.lotteup.com/manage/bank/executePayment
[ sql ] [ DB ] CONNECT:[ UseTime:0.000871s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001385s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/executepayment'  AND `state` = 1 [ RunTime:0.002650s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001557s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `order_number` = '202508031459301220192220'  AND `state` = 4 LIMIT 1 [ RunTime:0.000654s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_withdrawal_channel` [ RunTime:0.001362s ]
[ sql ] [ SQL ] SELECT * FROM `ly_withdrawal_channel` WHERE  `id` = 7  AND `state` = 1 LIMIT 1 [ RunTime:0.000483s ]
[ sql ] [ SQL ] SELECT * FROM `ly_withdrawal_channel` WHERE  `id` = 7 LIMIT 1 [ RunTime:0.000830s ]
[ sql ] [ SQL ] UPDATE `ly_user_withdrawals`  SET `aid` = 24 , `state` = 2 , `set_time` = ********** , `remarks` = '代付失败：BALANCE_NOT_ENOUGH，资金已退回'  WHERE  `id` = 299 [ RunTime:0.000753s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_total` [ RunTime:0.001445s ]
[ sql ] [ SQL ] SELECT `balance` FROM `ly_user_total` WHERE  `uid` = 1150 LIMIT 1 [ RunTime:0.000406s ]
[ sql ] [ SQL ] UPDATE `ly_user_total`  SET `balance` = `balance` + 55000  WHERE  `uid` = 1150 [ RunTime:0.000448s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001238s ]
[ sql ] [ SQL ] UPDATE `ly_trade_details`  SET `state` = 2 , `remarks` = '代付失败：BALANCE_NOT_ENOUGH，资金已退回'  WHERE  `order_number` = '202508031459301220192220' [ RunTime:0.000651s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_actionlog` [ RunTime:0.001027s ]
[ sql ] [ SQL ] INSERT INTO `ly_actionlog` (`username` , `time` , `ip` , `log` , `isadmin`) VALUES ('admin' , ********** , '0.0.0.0' , '执行订单号为202508031459301220192220的代付。处理状态：代付失败，资金已退回' , 1) [ RunTime:0.000405s ]
[ info ] WatchPay config loaded from file: {"enabled":true,"default_gateway":"https:\/\/api.watchglb.com","api_urls":{"pay":"https:\/\/api.watchglb.com\/pay\/web","transfer":"https:\/\/api.watchglb.com\/pay\/transfer","query_transfer":"https:\/\/api.watchglb.com\/query\/transfer","query_balance":"https:\/\/api.watchglb.com\/query\/balance"},"countries":{"ID":{"name":"\u5370\u5c3c","name_en":"Indonesia","currency":"IDR","merchant_id":"*********","pay_key":"YINGPTEMYQ7BAKOSHCAYZESK1WKU8XMK","withdrawal_key":"DLVPXKVOMNOFVEN2AGEQWQUASPN2EPWQ","gateway_url":"https:\/\/api.watchglb.com","notify_domain":"https:\/\/www.lotteup.com","min_amount":20000,"max_amount":********,"enabled":true,"pay_types":{"220":{"name":"\u7f51\u94f6","type":"online","enabled":true,"fee_rate":0.045,"requires_bank_code":true,"min_amount":10000,"max_amount":********},"223":{"name":"\u626b\u7801","type":"scan","enabled":true,"fee_rate":0.055,"requires_bank_code":false,"min_amount":10000,"max_amount":********},"200":{"name":"\u7f51\u94f6B2C\u4e00\u7c7b","type":"online","enabled":false,"requires_bank_code":true},"201":{"name":"\u4fbf\u5229\u5e97\u4e00\u7c7b","type":"offline","enabled":false,"requires_bank_code":false},"202":{"name":"OVO\u94b1\u5305\u4e00\u7c7b","type":"wallet","enabled":false,"requires_bank_code":false},"203":{"name":"QRIS\u626b\u7801\u4e00\u7c7b","type":"scan","enabled":false,"requires_bank_code":false},"240":{"name":"\u7f51\u94f6B2C\u4e09\u7c7b","type":"online","enabled":false,"requires_bank_code":true},"243":{"name":"QRIS\u626b\u7801\u4e09\u7c7b","type":"scan","enabled":false,"requires_bank_code":false}},"supported_banks":{"BCA":{"name":"Bank Central Asia","name_id":"Bank Central Asia","enabled":true},"MANDIRI":{"name":"Bank Mandiri","name_id":"Bank Mandiri","enabled":true},"BNI":{"name":"Bank Negara Indonesia","name_id":"Bank Negara Indonesia","enabled":true},"BRI":{"name":"Bank Rakyat Indonesia","name_id":"Bank Rakyat Indonesia","enabled":true},"PERMATA":{"name":"Bank Permata","name_id":"Bank Permata","enabled":true},"CIMB":{"name":"Bank CIMB Niaga","name_id":"Bank CIMB Niaga","enabled":true},"MAYBANK":{"name":"Bank Maybank","name_id":"Bank Maybank","enabled":true},"DANAMON":{"name":"Bank Danamon","name_id":"Bank Danamon","enabled":true},"BSI":{"name":"Bank Syariah Indonesia","name_id":"Bank Syariah Indonesia","enabled":true},"BNC":{"name":"Neo Commerce","name_id":"Bank Yudha Bhakti","enabled":true}}}},"notify_ips":["*************"]}
[ info ] Found bank code for bank_id 19: DANA -> DANA
[ info ] WatchPay sign debug - signStr: apply_date=2025-08-03 15:12:41&back_url=https://www.lotteup.com/api/transaction/unifiedWithdrawalCallback&bank_code=DANA&mch_id=*********&mch_transferId=202508031459301220192220&receive_account=*************&receive_name=aa&transfer_amount=50000&key=DLVPXKVOMNOFVEN2AGEQWQUASPN2EPWQ
[ info ] WatchPay sign debug - generated sign: 91b008d63d253a56e85305059f6dbfbe
[ error ] WatchPay withdrawal direct failed: {"signType":null,"sign":null,"respCode":"FAIL","mchId":null,"merTransferId":null,"transferAmount":null,"applyDate":null,"tradeNo":null,"tradeResult":null,"errorMsg":"BALANCE_NOT_ENOUGH"}
---------------------------------------------------------------

[2025-08-03T15:13:02+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000814s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001405s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= ********** LIMIT 100 [ RunTime:0.000670s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001795s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000564s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000409s ]
---------------------------------------------------------------

[2025-08-03T15:13:13+08:00] ************** POST lotteup.com/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000672s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001093s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000816s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001291s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.001076s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.001733s ]
---------------------------------------------------------------

[2025-08-03T15:13:16+08:00] 2401:b60:17::204 POST www.lotteup.com/manage/bank/executePayment
[ sql ] [ DB ] CONNECT:[ UseTime:0.000812s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001170s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/executepayment'  AND `state` = 1 [ RunTime:0.000840s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001438s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `order_number` = '202508031459301220192220'  AND `state` = 4 LIMIT 1 [ RunTime:0.000726s ]
---------------------------------------------------------------

[2025-08-03T15:13:22+08:00] 2401:b60:17::204 GET www.lotteup.com/manage/Bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000812s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001584s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.001249s ]
---------------------------------------------------------------

[2025-08-03T15:13:22+08:00] 2401:b60:17::204 GET www.lotteup.com/manage/withdrawal_channel/getEnabledChannels
[ sql ] [ DB ] CONNECT:[ UseTime:0.000872s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001174s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'WithdrawalChannel/getenabledchannels'  AND `state` = 1 [ RunTime:0.000875s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_withdrawal_channel` [ RunTime:0.001414s ]
[ sql ] [ SQL ] SELECT `id`,`name`,`code`,`mode`,`min_amount`,`max_amount`,`fee_rate` FROM `ly_withdrawal_channel` WHERE  `state` = 1 ORDER BY `sort` ASC [ RunTime:0.000519s ]
---------------------------------------------------------------

[2025-08-03T15:13:22+08:00] 2401:b60:17::204 POST www.lotteup.com/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.001059s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001647s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.001126s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001528s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.000971s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.002032s ]
---------------------------------------------------------------

[2025-08-03T15:13:26+08:00] 2401:b60:17::204 GET www.lotteup.com/manage/bank/withdrawalsDetails?id=300
[ sql ] [ DB ] CONNECT:[ UseTime:0.000846s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001598s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/withdrawalsdetails'  AND `state` = 1 [ RunTime:0.001098s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001360s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 300 LIMIT 1 [ RunTime:0.000642s ]
---------------------------------------------------------------

[2025-08-03T15:13:30+08:00] 2401:b60:17::204 GET www.lotteup.com/manage/bank/withdrawalsDetails?id=299
[ sql ] [ DB ] CONNECT:[ UseTime:0.000575s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001052s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/withdrawalsdetails'  AND `state` = 1 [ RunTime:0.000780s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001068s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 299 LIMIT 1 [ RunTime:0.000461s ]
---------------------------------------------------------------

[2025-08-03T15:13:34+08:00] ************** POST www.lotteup.com/api/transaction/unifiedWithdrawalCallback
[ info ] BaseController action: unifiedwithdrawalcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Current action: unifiedwithdrawalcallback
[ info ] Unified withdrawal callback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031506301400899836","feeType":"1","number":"*************","platOrderNum":"YBL1951902536864641079","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"JI7wpyqiMryARy6GYHungd9XsMqg4GVvXSP7gG2aCxG6L5MK\/fBlmm5DPaSkamFRqEBYDIWfkf+1rpKUwuCxyA2w6tg8KLHmjbIgz28c9u6G4eIx3cY3aJ3D0M2p4xxIKSz9mnycKKLO\/kd+\/fJycZdhAaxL4vRmEleXn1ApTH8=","status":"4"}
[ info ] TransactionService handleWithdrawalCallback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031506301400899836","feeType":"1","number":"*************","platOrderNum":"YBL1951902536864641079","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"JI7wpyqiMryARy6GYHungd9XsMqg4GVvXSP7gG2aCxG6L5MK\/fBlmm5DPaSkamFRqEBYDIWfkf+1rpKUwuCxyA2w6tg8KLHmjbIgz28c9u6G4eIx3cY3aJ3D0M2p4xxIKSz9mnycKKLO\/kd+\/fJycZdhAaxL4vRmEleXn1ApTH8=","status":"4"}
[ info ] Detected withdrawal channel type: jaya_pay
[ info ] JayaPay sign string: 014代付下单7000150000aa*************202508031506301400899836YBL19519025368646410794FAILED
[ error ] JayaPay decrypt sign error: RSA public decrypt failed
[ error ] JayaPay decrypt sign debug - encryptedSign length: 172
[ error ] JayaPay decrypt sign debug - publicKey: -----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQU...
[ error ] JayaPay verify sign error: RSA public decrypt failed
[ error ] JayaPay withdrawal notify sign verify failed
---------------------------------------------------------------

[2025-08-03T15:13:36+08:00] ************** POST www.lotteup.com/api/transaction/unifiedWithdrawalCallback
[ info ] BaseController action: unifiedwithdrawalcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Current action: unifiedwithdrawalcallback
[ info ] Unified withdrawal callback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031211500962316354","feeType":"1","number":"*************","platOrderNum":"YBL1951858600607498252","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"K0HLRCzhz\/5NT561l511TTBDd+zjmqgffJ8M6gJonMYDkw8sybud8jYCleGesQos+IoqQJag+Y7+o+A+I1\/fCNvKNDc1oum0qBTz\/uC0ViC4uKzX38z1+nWUt3XY9GFMTbvwhxGUZw2cKjIa\/lBAiPiD65glVHKtalZ2CdW+eE8=","status":"4"}
[ info ] TransactionService handleWithdrawalCallback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031211500962316354","feeType":"1","number":"*************","platOrderNum":"YBL1951858600607498252","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"K0HLRCzhz\/5NT561l511TTBDd+zjmqgffJ8M6gJonMYDkw8sybud8jYCleGesQos+IoqQJag+Y7+o+A+I1\/fCNvKNDc1oum0qBTz\/uC0ViC4uKzX38z1+nWUt3XY9GFMTbvwhxGUZw2cKjIa\/lBAiPiD65glVHKtalZ2CdW+eE8=","status":"4"}
[ info ] Detected withdrawal channel type: jaya_pay
[ info ] JayaPay sign string: 014代付下单7000150000aa*************202508031211500962316354YBL19518586006074982524FAILED
[ error ] JayaPay decrypt sign error: RSA public decrypt failed
[ error ] JayaPay decrypt sign debug - encryptedSign length: 172
[ error ] JayaPay decrypt sign debug - publicKey: -----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQU...
[ error ] JayaPay verify sign error: RSA public decrypt failed
[ error ] JayaPay withdrawal notify sign verify failed
---------------------------------------------------------------

[2025-08-03T15:13:40+08:00] ************** POST lotteup.com/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000798s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001335s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000879s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001214s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.000868s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.001562s ]
---------------------------------------------------------------

[2025-08-03T15:13:41+08:00] ************** POST lotteup.com/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000827s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001546s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.001107s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001302s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.000876s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.001740s ]
---------------------------------------------------------------

[2025-08-03T15:13:42+08:00] ************** POST lotteup.com/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000681s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001150s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000815s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001058s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.000709s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.001331s ]
---------------------------------------------------------------

[2025-08-03T15:13:43+08:00] ************** POST lotteup.com/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000589s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001252s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000954s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001050s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.000674s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.001545s ]
---------------------------------------------------------------

[2025-08-03T15:13:44+08:00] ************** POST lotteup.com/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000612s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001228s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000781s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001364s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.000694s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.001587s ]
---------------------------------------------------------------

[2025-08-03T15:13:46+08:00] ************** POST lotteup.com/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000742s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001350s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000944s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001465s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.001368s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` ORDER BY `time` DESC LIMIT 10,10 [ RunTime:0.001733s ]
---------------------------------------------------------------

[2025-08-03T15:15:00+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.002093s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000613s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= ********** LIMIT 100 [ RunTime:0.000748s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000842s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000223s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000213s ]
---------------------------------------------------------------

[2025-08-03T15:16:02+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000883s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000543s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754205361 LIMIT 100 [ RunTime:0.000378s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001060s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000394s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000574s ]
---------------------------------------------------------------

[2025-08-03T15:17:03+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000961s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000838s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754205423 LIMIT 100 [ RunTime:0.000695s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001207s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000476s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000597s ]
---------------------------------------------------------------

[2025-08-03T15:38:41+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001021s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000730s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754206721 LIMIT 100 [ RunTime:0.000406s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000863s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000279s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000538s ]
---------------------------------------------------------------

[2025-08-03T15:39:43+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000949s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000618s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754206782 LIMIT 100 [ RunTime:0.000270s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001109s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000267s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000308s ]
---------------------------------------------------------------

[2025-08-03T15:40:45+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000883s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000594s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754206844 LIMIT 100 [ RunTime:0.000232s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000846s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000232s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000237s ]
---------------------------------------------------------------

[2025-08-03T15:41:34+08:00] ********** POST dianzhan_nginx/api/transaction/unifiedWithdrawalCallback
[ info ] BaseController action: unifiedwithdrawalcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Current action: unifiedwithdrawalcallback
[ info ] Unified withdrawal callback received: {"msg":"SUCCESS","code":"00","method":"BANK_TRANSFER","orderNum":"**********************","platOrderNum":"PTCDC4D879BD4AB8","payFee":"5000.00","payMoney":"95000","originalAmount":"100000","phone":"08**********","name":"TestUser","bankCode":"BCA","bankAccount":"**********","bankName":"Bank Central Asia","platSign":"ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt\/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=","email":"<EMAIL>","status":"SUCCESS"}
[ info ] TransactionService handleWithdrawalCallback received: {"msg":"SUCCESS","code":"00","method":"BANK_TRANSFER","orderNum":"**********************","platOrderNum":"PTCDC4D879BD4AB8","payFee":"5000.00","payMoney":"95000","originalAmount":"100000","phone":"08**********","name":"TestUser","bankCode":"BCA","bankAccount":"**********","bankName":"Bank Central Asia","platSign":"ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt\/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=","email":"<EMAIL>","status":"SUCCESS"}
[ info ] Detected withdrawal channel type: jaya_pay
[ info ] JayaPay sign string: **********<NAME_EMAIL>_TRANSFERSUCCESSTestUser**********************1000005000.009500008**********PTCDC4D879BD4AB8SUCCESS
[ info ] JayaPay verify - sign string: **********<NAME_EMAIL>_TRANSFERSUCCESSTestUser**********************1000005000.009500008**********PTCDC4D879BD4AB8SUCCESS
[ info ] JayaPay verify - decrypted sign: 00user@example.comQRISSUCCESSUserJP202508031503451773592250.235000508**********PT1B168C55D700405ASUCCESS
[ error ] JayaPay withdrawal notify sign verify failed
---------------------------------------------------------------

[2025-08-03T15:41:45+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001056s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000633s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754206905 LIMIT 100 [ RunTime:0.000392s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001029s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000251s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000392s ]
---------------------------------------------------------------

[2025-08-03T15:42:46+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001124s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000835s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754206966 LIMIT 100 [ RunTime:0.000472s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001211s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000243s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000369s ]
---------------------------------------------------------------

[2025-08-03T15:43:39+08:00] ********** POST dianzhan_nginx/api/transaction/unifiedWithdrawalCallback
[ info ] BaseController action: unifiedwithdrawalcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Current action: unifiedwithdrawalcallback
[ info ] Unified withdrawal callback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031506301400899836","feeType":"1","number":"*************","platOrderNum":"YBL1951902536864641079","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"JI7wpyqiMryARy6GYHungd9XsMqg4GVvXSP7gG2aCxG6L5MK\/fBlmm5DPaSkamFRqEBYDIWfkf+1rpKUwuCxyA2w6tg8KLHmjbIgz28c9u6G4eIx3cY3aJ3D0M2p4xxIKSz9mnycKKLO\/kd+\/fJycZdhAaxL4vRmEleXn1ApTH8=","status":"4"}
[ info ] TransactionService handleWithdrawalCallback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031506301400899836","feeType":"1","number":"*************","platOrderNum":"YBL1951902536864641079","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"JI7wpyqiMryARy6GYHungd9XsMqg4GVvXSP7gG2aCxG6L5MK\/fBlmm5DPaSkamFRqEBYDIWfkf+1rpKUwuCxyA2w6tg8KLHmjbIgz28c9u6G4eIx3cY3aJ3D0M2p4xxIKSz9mnycKKLO\/kd+\/fJycZdhAaxL4vRmEleXn1ApTH8=","status":"4"}
[ info ] Detected withdrawal channel type: jaya_pay
[ info ] JayaPay sign string: 014代付下单7000150000aa*************202508031506301400899836YBL19519025368646410794FAILED
[ info ] JayaPay verify - sign string: 014代付下单7000150000aa*************202508031506301400899836YBL19519025368646410794FAILED
[ info ] JayaPay verify - decrypted sign: 014代付下单7000150000aa*************202508031506301400899836YBL19519025368646410794FAILED
[ sql ] [ DB ] CONNECT:[ UseTime:0.000977s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000727s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `order_number` = '202508031506301400899836' LIMIT 1 [ RunTime:0.000275s ]
[ sql ] [ SQL ] UPDATE `ly_user_withdrawals`  SET `state` = 2 , `set_time` = 1754207019 , `remarks` = 'JayaPay代付失败，状态：4'  WHERE  `id` = 300 [ RunTime:0.000277s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_total` [ RunTime:0.000715s ]
[ sql ] [ SQL ] UPDATE `ly_user_total`  SET `balance` = `balance` + 55000  WHERE  `uid` = 1150 [ RunTime:0.000341s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_total` WHERE  `uid` = 1150 LIMIT 1 [ RunTime:0.000452s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000729s ]
[ error ] JayaPay withdrawal failed transaction error: fields not exists:[type]
---------------------------------------------------------------

[2025-08-03T15:43:47+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001108s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000558s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754207027 LIMIT 100 [ RunTime:0.000239s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000902s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000294s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000493s ]
---------------------------------------------------------------

[2025-08-03T15:44:49+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001022s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000508s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754207088 LIMIT 100 [ RunTime:0.000394s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001125s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000432s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.001106s ]
---------------------------------------------------------------

[2025-08-03T15:45:42+08:00] ********** POST dianzhan_nginx/api/transaction/unifiedWithdrawalCallback
[ info ] BaseController action: unifiedwithdrawalcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Current action: unifiedwithdrawalcallback
[ info ] Unified withdrawal callback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031506301400899836","feeType":"1","number":"*************","platOrderNum":"YBL1951902536864641079","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"JI7wpyqiMryARy6GYHungd9XsMqg4GVvXSP7gG2aCxG6L5MK\/fBlmm5DPaSkamFRqEBYDIWfkf+1rpKUwuCxyA2w6tg8KLHmjbIgz28c9u6G4eIx3cY3aJ3D0M2p4xxIKSz9mnycKKLO\/kd+\/fJycZdhAaxL4vRmEleXn1ApTH8=","status":"4"}
[ info ] TransactionService handleWithdrawalCallback received: {"bankCode":"014","fee":"7000","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"202508031506301400899836","feeType":"1","number":"*************","platOrderNum":"YBL1951902536864641079","money":"50000","statusMsg":"FAILED","name":"aa","platSign":"JI7wpyqiMryARy6GYHungd9XsMqg4GVvXSP7gG2aCxG6L5MK\/fBlmm5DPaSkamFRqEBYDIWfkf+1rpKUwuCxyA2w6tg8KLHmjbIgz28c9u6G4eIx3cY3aJ3D0M2p4xxIKSz9mnycKKLO\/kd+\/fJycZdhAaxL4vRmEleXn1ApTH8=","status":"4"}
[ info ] Detected withdrawal channel type: jaya_pay
[ info ] JayaPay sign string: 014代付下单7000150000aa*************202508031506301400899836YBL19519025368646410794FAILED
[ info ] JayaPay verify - sign string: 014代付下单7000150000aa*************202508031506301400899836YBL19519025368646410794FAILED
[ info ] JayaPay verify - decrypted sign: 014代付下单7000150000aa*************202508031506301400899836YBL19519025368646410794FAILED
[ sql ] [ DB ] CONNECT:[ UseTime:0.001352s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000854s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `order_number` = '202508031506301400899836' LIMIT 1 [ RunTime:0.000280s ]
[ sql ] [ SQL ] UPDATE `ly_user_withdrawals`  SET `state` = 2 , `set_time` = 1754207142 , `remarks` = 'JayaPay代付失败，状态：4'  WHERE  `id` = 300 [ RunTime:0.000460s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_total` [ RunTime:0.000793s ]
[ sql ] [ SQL ] UPDATE `ly_user_total`  SET `balance` = `balance` + 55000  WHERE  `uid` = 1150 [ RunTime:0.000279s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_total` WHERE  `uid` = 1150 LIMIT 1 [ RunTime:0.000318s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001014s ]
[ error ] JayaPay withdrawal failed transaction error: fields not exists:[type]
---------------------------------------------------------------

[2025-08-03T15:45:50+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001174s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000621s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754207150 LIMIT 100 [ RunTime:0.000200s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000934s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000238s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000332s ]
---------------------------------------------------------------

[2025-08-03T15:46:52+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001305s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001907s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754207212 LIMIT 100 [ RunTime:0.000312s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001382s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000337s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000356s ]
---------------------------------------------------------------

[2025-08-03T15:47:53+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001015s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000716s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754207273 LIMIT 100 [ RunTime:0.000324s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001028s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000374s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000330s ]
---------------------------------------------------------------

[2025-08-03T15:48:09+08:00] ********** POST dianzhan_nginx/api/transaction/unifiedWithdrawalCallback
[ info ] BaseController action: unifiedwithdrawalcallback
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] Current action: unifiedwithdrawalcallback
[ info ] Unified withdrawal callback received: {"bankCode":"014","fee":"2250.23","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"JP20250803150345177359","feeType":"1","number":"08**********","platOrderNum":"PT1B168C55D700405A","money":"50005","statusMsg":"SUCCESS","name":"User","platSign":"RcgvcJza5cGsTC5JHp5v2nOIhU2n44ea77ZNngjL0i7fwl8NV62gl5upduULXAGJGG3v0\/sGV8Hd8IUTC6mqeRRBbwErMJg96TJb4gVi+kU37pZnukteJD46IIP1o\/tsbxUExmk0CYD3NJtSPa6wQoWIpDvZThNBg0WCEzmtF8gNsuPBkYtwcDj6VJ7UdoQKy5\/v8mbf4QK4lvBngzxmoND234eHs0BQCNEwQZQvBPTdSFIG+9jsM1mvZtrjnemlK\/qwflbhiVqzE2bVQOnTy5W6BrlV\/cR5ihY93AXo+9GY0McL3EucNRJLeWALk15KgbIwe+NewKXSzdvyh36g0C4Udv\/Z1CPaBB408Q3m8Ks85hyenHj6m7I5gfr\/ABAgCXsn7mHIYQ9Fflbdwa5hHnTn78BgGdPICYW8iCnnj1\/CxAGSmTgsnfqCJjQoL7uXp9GNqATHXOgGu2BHkyGkyNSdRA4nojG7MmwdC4Kpy6\/Xb1giVeAaPCa+Cw4NfpGc","email":"<EMAIL>","status":"2"}
[ info ] TransactionService handleWithdrawalCallback received: {"bankCode":"014","fee":"2250.23","description":"\u4ee3\u4ed8\u4e0b\u5355","orderNum":"JP20250803150345177359","feeType":"1","number":"08**********","platOrderNum":"PT1B168C55D700405A","money":"50005","statusMsg":"SUCCESS","name":"User","platSign":"RcgvcJza5cGsTC5JHp5v2nOIhU2n44ea77ZNngjL0i7fwl8NV62gl5upduULXAGJGG3v0\/sGV8Hd8IUTC6mqeRRBbwErMJg96TJb4gVi+kU37pZnukteJD46IIP1o\/tsbxUExmk0CYD3NJtSPa6wQoWIpDvZThNBg0WCEzmtF8gNsuPBkYtwcDj6VJ7UdoQKy5\/v8mbf4QK4lvBngzxmoND234eHs0BQCNEwQZQvBPTdSFIG+9jsM1mvZtrjnemlK\/qwflbhiVqzE2bVQOnTy5W6BrlV\/cR5ihY93AXo+9GY0McL3EucNRJLeWALk15KgbIwe+NewKXSzdvyh36g0C4Udv\/Z1CPaBB408Q3m8Ks85hyenHj6m7I5gfr\/ABAgCXsn7mHIYQ9Fflbdwa5hHnTn78BgGdPICYW8iCnnj1\/CxAGSmTgsnfqCJjQoL7uXp9GNqATHXOgGu2BHkyGkyNSdRA4nojG7MmwdC4Kpy6\/Xb1giVeAaPCa+Cw4NfpGc","email":"<EMAIL>","status":"2"}
[ info ] Detected withdrawal channel type: jaya_pay
[ info ] JayaPay sign string: 014代付下单user@example.com2250.23150005User08**********JP20250803150345177359PT1B168C55D700405A2SUCCESS
[ info ] JayaPay verify - sign string: 014代付下单user@example.com2250.23150005User08**********JP20250803150345177359PT1B168C55D700405A2SUCCESS
[ info ] JayaPay verify - decrypted sign: user@example.comUserJP2025080315034517735950005PT1B168C55D700405ASUCCESSRequest Transaction Success在线充值https://uaw28.glorioushop.com/cash/?orderNum=PT1B168C55D700405A&SG=770dbe376ee52c1e11c4bb95eaebbb7c&SX=0efcce7a14fc5d66e2fcee10a680b9d9&SV=5ac797a2b8702bcbdcd682e90e1b19e4&SN=a091090fbd4e516db87ac0fa36777193
[ error ] JayaPay withdrawal notify sign verify failed
---------------------------------------------------------------

[2025-08-03T15:48:55+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001033s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000735s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754207334 LIMIT 100 [ RunTime:0.000850s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001170s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000308s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000331s ]
---------------------------------------------------------------

[2025-08-03T15:49:57+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001233s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000875s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754207396 LIMIT 100 [ RunTime:0.000413s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000985s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000582s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000412s ]
---------------------------------------------------------------

[2025-08-03T15:50:59+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001069s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000673s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754207458 LIMIT 100 [ RunTime:0.000253s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001263s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000365s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000243s ]

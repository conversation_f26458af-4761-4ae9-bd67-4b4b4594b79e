<?php
/**
 * 测试真实回调接口
 */

// 真实回调数据
$callbackData = [
    "msg" => "SUCCESS",
    "code" => "00",
    "method" => "QRIS",
    "orderNum" => "JP20250803150345177359",
    "platOrderNum" => "PT1B168C55D700405A",
    "payFee" => "2250.23",
    "payMoney" => "50005",
    "phone" => "081234567890",
    "name" => "User",
    "platSign" => "ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=",
    "email" => "<EMAIL>",
    "status" => "SUCCESS"
];

echo "=== 测试真实回调接口 ===\n";
echo "订单号: {$callbackData['orderNum']}\n";
echo "平台订单号: {$callbackData['platOrderNum']}\n";
echo "金额: {$callbackData['payMoney']}\n\n";

// 发送POST请求到回调接口
$url = 'http://localhost/api/transaction/unifiedCallback';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($callbackData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "请求URL: {$url}\n";
echo "HTTP状态码: {$httpCode}\n";

if ($error) {
    echo "CURL错误: {$error}\n";
} else {
    echo "响应内容: {$response}\n";
}

if ($httpCode === 200 && $response === 'success') {
    echo "\n✅ 回调接口测试成功！\n";
} else {
    echo "\n❌ 回调接口测试失败\n";
}

echo "\n=== 测试完成 ===\n";

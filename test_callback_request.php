<?php
/**
 * 测试真实回调请求
 * 模拟JayaPay发送回调到我们的接口
 */

// 真实回调数据
$callbackData = [
    "msg" => "SUCCESS",
    "code" => "00", 
    "method" => "QRIS",
    "orderNum" => "JP20250803150345177359",
    "platOrderNum" => "PT1B168C55D700405A",
    "payFee" => "2250.23",
    "payMoney" => "50005",
    "phone" => "081234567890",
    "name" => "User",
    "platSign" => "ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=",
    "email" => "<EMAIL>",
    "status" => "SUCCESS"
];

echo "=== 测试JayaPay回调请求 ===\n";
echo "订单号: {$callbackData['orderNum']}\n";
echo "平台订单号: {$callbackData['platOrderNum']}\n";
echo "金额: {$callbackData['payMoney']}\n\n";

// 使用容器内部网络访问
$url = 'http://dianzhan_nginx/api/transaction/unifiedCallback';

echo "发送回调请求到: {$url}\n";

$postData = http_build_query($callbackData);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded',
    'User-Agent: JayaPay-Callback/1.0'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: {$httpCode}\n";

if ($error) {
    echo "CURL错误: {$error}\n";
} else {
    echo "响应内容: {$response}\n";
}

if ($httpCode === 200) {
    if ($response === 'success') {
        echo "\n✅ 回调处理成功！JayaPay回调修复完成！\n";
    } else {
        echo "\n⚠️ 回调接收成功，但处理结果: {$response}\n";
    }
} else {
    echo "\n❌ 回调请求失败\n";
}

echo "\n=== 测试完成 ===\n";

<?php
/**
 * 测试创建充值订单并测试回调
 */

echo "=== 测试创建充值订单并回调 ===\n\n";

// 首先创建一个充值订单
$orderNum = "JP" . date('YmdHis') . rand(100000, 999999);
$platOrderNum = "PT" . strtoupper(substr(md5(time()), 0, 14));

echo "1. 创建测试充值订单\n";
echo "订单号: {$orderNum}\n";
echo "平台订单号: {$platOrderNum}\n\n";

// 模拟在数据库中创建订单
$createOrderSql = "
INSERT INTO ly_user_recharge (
    uid, order_number, money, daozhang_money, fee, type, add_time, state, remarks
) VALUES (
    1149, '{$orderNum}', 75000, 75000, 0, 118, " . time() . ", 0, 'JayaPay测试充值订单'
)";

echo "SQL: {$createOrderSql}\n\n";

// 通过Docker执行SQL
$sqlFile = '/tmp/create_test_order.sql';
file_put_contents($sqlFile, $createOrderSql);

$createResult = shell_exec("docker exec -i dianzhan_mysql mysql -u root -proot dianzhan < {$sqlFile}");
echo "创建订单结果: " . ($createResult ? $createResult : "成功") . "\n\n";

// 等待1秒确保订单创建完成
sleep(1);

echo "2. 测试充值回调\n";

// 准备回调数据
$callbackData = [
    "msg" => "SUCCESS",
    "code" => "00", 
    "method" => "QRIS",
    "orderNum" => $orderNum,
    "platOrderNum" => $platOrderNum,
    "payFee" => "3375.50",
    "payMoney" => "75000",
    "phone" => "081234567890",
    "name" => "TestUser",
    "platSign" => "ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=",
    "email" => "<EMAIL>",
    "status" => "SUCCESS"
];

echo "回调数据:\n";
foreach ($callbackData as $key => $value) {
    echo "  {$key}: {$value}\n";
}
echo "\n";

// 发送回调请求
$url = 'http://dianzhan_nginx/api/transaction/unifiedCallback';
$postData = http_build_query($callbackData);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded',
    'User-Agent: JayaPay-Callback/1.0'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "3. 回调结果\n";
echo "HTTP状态码: {$httpCode}\n";

if ($error) {
    echo "❌ CURL错误: {$error}\n";
} else {
    echo "响应内容: {$response}\n";
    
    if ($httpCode === 200) {
        if ($response === 'SUCCESS') {
            echo "✅ 充值回调处理成功！\n\n";
            
            // 验证订单状态
            echo "4. 验证订单状态\n";
            $checkSql = "SELECT id, state, dispose_time, remarks FROM ly_user_recharge WHERE order_number = '{$orderNum}'";
            file_put_contents('/tmp/check_order.sql', $checkSql);
            $checkResult = shell_exec("docker exec -i dianzhan_mysql mysql -u root -proot dianzhan -e \"{$checkSql}\"");
            echo "订单状态查询结果:\n{$checkResult}\n";
            
            // 验证用户余额变化
            echo "5. 验证用户余额\n";
            $balanceSql = "SELECT balance, total_balance FROM ly_user_total WHERE uid = 1149";
            file_put_contents('/tmp/check_balance.sql', $balanceSql);
            $balanceResult = shell_exec("docker exec -i dianzhan_mysql mysql -u root -proot dianzhan -e \"{$balanceSql}\"");
            echo "用户余额查询结果:\n{$balanceResult}\n";
            
        } else {
            echo "⚠️ 回调接收成功，但处理结果: {$response}\n";
        }
    } else {
        echo "❌ 回调请求失败\n";
    }
}

// 清理临时文件
@unlink('/tmp/create_test_order.sql');
@unlink('/tmp/check_order.sql');
@unlink('/tmp/check_balance.sql');

echo "\n=== 测试完成 ===\n";

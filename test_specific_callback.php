<?php
/**
 * 测试特定订单的充值回调
 */

echo "=== 测试特定订单充值回调 ===\n\n";

// 使用刚创建的订单号
$orderNum = "JP20250803153500123456";
$platOrderNum = "PT" . strtoupper(substr(md5($orderNum), 0, 14));

echo "测试订单号: {$orderNum}\n";
echo "平台订单号: {$platOrderNum}\n";
echo "充值金额: 75,000 IDR\n\n";

// 准备回调数据
$callbackData = [
    "msg" => "SUCCESS",
    "code" => "00", 
    "method" => "QRIS",
    "orderNum" => $orderNum,
    "platOrderNum" => $platOrderNum,
    "payFee" => "3375.50",
    "payMoney" => "75000",
    "phone" => "081234567890",
    "name" => "TestUser",
    "platSign" => "ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=",
    "email" => "<EMAIL>",
    "status" => "SUCCESS"
];

echo "发送回调请求...\n";

// 发送回调请求
$url = 'http://dianzhan_nginx/api/transaction/unifiedCallback';
$postData = http_build_query($callbackData);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded',
    'User-Agent: JayaPay-Callback/1.0'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: {$httpCode}\n";

if ($error) {
    echo "❌ CURL错误: {$error}\n";
} else {
    echo "响应内容: {$response}\n";
    
    if ($httpCode === 200) {
        if ($response === 'SUCCESS') {
            echo "✅ 充值回调处理成功！\n";
        } else {
            echo "⚠️ 回调接收成功，但处理结果: {$response}\n";
        }
    } else {
        echo "❌ 回调请求失败\n";
    }
}

echo "\n=== 测试完成 ===\n";

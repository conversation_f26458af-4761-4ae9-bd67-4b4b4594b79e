<?php
/**
 * 简化的JayaPay回调修复测试脚本
 * 不依赖ThinkPHP框架
 */

class SimpleJayaPayTest
{
    private $platformPublicKey;

    public function __construct()
    {
        $this->platformPublicKey = $this->getPlatformPublicKey();
    }

    /**
     * 获取平台公钥
     */
    private function getPlatformPublicKey()
    {
        try {
            $configPath = 'config/payment_config.php';
            if (!file_exists($configPath)) {
                return null;
            }

            $paymentConfig = include($configPath);
            $jayaPayConfig = $paymentConfig['jaya_pay'] ?? null;

            if ($jayaPayConfig && $jayaPayConfig['enabled']) {
                return $jayaPayConfig['platform_public_key'] ?? '';
            }

            return null;
        } catch (Exception $e) {
            echo "获取平台公钥错误: " . $e->getMessage() . "\n";
            return null;
        }
    }

    /**
     * 构建签名字符串
     */
    private function buildSignString($params)
    {
        // 移除签名字段
        unset($params['platSign']);
        unset($params['sign']);

        // 过滤空值
        $params = array_filter($params, function($value) {
            return $value !== '' && $value !== null;
        });

        // 按键名排序
        ksort($params);

        // JayaPay签名字符串构建方式：只连接值，不包含键名
        $signString = '';
        foreach ($params as $value) {
            $signString .= $value;
        }

        return $signString;
    }

    /**
     * 解密RSA签名
     */
    private function decryptRSASign($encryptedSign, $publicKey)
    {
        try {
            // 确保公钥格式正确
            if (strpos($publicKey, '-----BEGIN') === false) {
                $publicKey = "-----BEGIN PUBLIC KEY-----\n" . chunk_split($publicKey, 64, "\n") . "-----END PUBLIC KEY-----\n";
            }

            $publicKeyResource = openssl_pkey_get_public($publicKey);
            if (!$publicKeyResource) {
                throw new Exception('Invalid public key');
            }

            // Base64解码
            $encryptedData = base64_decode($encryptedSign);
            
            // 获取密钥详情用于分块处理
            $keyDetails = openssl_pkey_get_details($publicKeyResource);
            $keySize = $keyDetails['bits'];
            $maxBlock = intval($keySize / 8); // RSA解密时的最大块大小

            $decryptedData = '';
            $dataLength = strlen($encryptedData);
            
            // 分块解密
            for ($i = 0; $i < $dataLength; $i += $maxBlock) {
                $block = substr($encryptedData, $i, $maxBlock);
                $decryptedBlock = '';
                
                if (!openssl_public_decrypt($block, $decryptedBlock, $publicKeyResource)) {
                    throw new Exception('RSA public decrypt failed: ' . openssl_error_string());
                }
                
                $decryptedData .= $decryptedBlock;
            }

            return $decryptedData;

        } catch (Exception $e) {
            echo "RSA解密错误: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 验证RSA签名
     */
    public function verifyRSASign($params, $publicKey)
    {
        try {
            if (!isset($params['platSign']) || empty($params['platSign'])) {
                echo "缺少platSign参数\n";
                return false;
            }

            $platSign = $params['platSign'];
            $signParams = $params;
            unset($signParams['platSign']);

            // 构建签名字符串
            $signString = $this->buildSignString($signParams);
            
            // 使用平台公钥解密签名
            $decryptedSign = $this->decryptRSASign($platSign, $publicKey);
            
            echo "签名字符串: " . $signString . "\n";
            echo "解密后签名: " . $decryptedSign . "\n";
            
            return $signString === $decryptedSign;

        } catch (Exception $e) {
            echo "验证签名错误: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 运行测试
     */
    public function runTest()
    {
        echo "=== 简化JayaPay回调修复测试 ===\n\n";

        // 测试1：验证平台公钥获取
        echo "1. 测试平台公钥获取:\n";
        if ($this->platformPublicKey) {
            echo "✅ 平台公钥获取成功\n";
            echo "公钥前50字符: " . substr($this->platformPublicKey, 0, 50) . "...\n";
        } else {
            echo "❌ 平台公钥获取失败\n";
            return;
        }

        echo "\n";

        // 测试2：使用真实回调数据测试签名验证
        echo "2. 测试真实回调数据签名验证:\n";

        $callbackData = [
            "msg" => "SUCCESS",
            "code" => "00",
            "method" => "QRIS",
            "orderNum" => "JP20250803150345177359",
            "platOrderNum" => "PT1B168C55D700405A",
            "payFee" => "2250.23",
            "payMoney" => "50005",
            "phone" => "081234567890",
            "name" => "User",
            "platSign" => "ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=",
            "email" => "<EMAIL>",
            "status" => "SUCCESS"
        ];

        echo "订单号: {$callbackData['orderNum']}\n";
        echo "平台订单号: {$callbackData['platOrderNum']}\n";
        echo "金额: {$callbackData['payMoney']}\n\n";

        // 测试签名验证
        $verifyResult = $this->verifyRSASign($callbackData, $this->platformPublicKey);
        
        if ($verifyResult) {
            echo "\n✅ 签名验证成功！修复生效！\n";
        } else {
            echo "\n❌ 签名验证失败\n";
        }

        echo "\n=== 测试完成 ===\n";
    }
}

// 运行测试
$test = new SimpleJayaPayTest();
$test->runTest();

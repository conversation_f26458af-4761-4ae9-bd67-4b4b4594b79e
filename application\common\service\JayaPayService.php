<?php
namespace app\common\service;

class JayaPayService
{
    /**
     * 获取JayaPay配置
     */
    public function getConfig()
    {
        try {
            // 直接读取配置文件
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (!file_exists($configPath)) {
                return null;
            }

            $paymentConfig = include($configPath);
            $jayaPayConfig = $paymentConfig['jaya_pay'] ?? null;

            if ($jayaPayConfig && $jayaPayConfig['enabled']) {
                $merchantConfig = $jayaPayConfig['merchants']['default'] ?? null;
                if ($merchantConfig && $merchantConfig['enabled']) {
                    return [
                        'merchant_code' => $merchantConfig['merchant_code'],
                        'private_key' => $merchantConfig['private_key'],
                        'public_key' => $merchantConfig['public_key'] ?? '',
                        'notify_url' => $merchantConfig['notify_url'] ?? '',
                        'gateway_urls' => $jayaPayConfig['gateway_urls']
                    ];
                }
            }

            return null;

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay config error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 验证配置是否完整
     */
    public function validateConfig()
    {
        $config = $this->getConfig();
        if (!$config) {
            return false;
        }

        $required = ['merchant_code', 'private_key', 'public_key'];
        foreach ($required as $field) {
            if (empty($config[$field])) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取JayaPay平台公钥（用于验证回调签名）
     */
    public function getPlatformPublicKey()
    {
        try {
            // 直接读取配置文件
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (!file_exists($configPath)) {
                return null;
            }

            $paymentConfig = include($configPath);
            $jayaPayConfig = $paymentConfig['jaya_pay'] ?? null;

            if ($jayaPayConfig && $jayaPayConfig['enabled']) {
                return $jayaPayConfig['platform_public_key'] ?? '';
            }

            return null;

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay getPlatformPublicKey error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取支持的支付方式
     */
    public function getPaymentMethods($lang = 'id')
    {
        $texts = $this->getTexts($lang);

        try {
            // 直接读取配置文件，修复路径问题
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (file_exists($configPath)) {
                $paymentConfig = include($configPath);
                $jayaPayConfig = $paymentConfig['jaya_pay'] ?? [];
            } else {
                $jayaPayConfig = [];
            }
            $paymentMethods = [];

            // 获取支持的支付方式（使用新的pay_types配置）
            $payTypes = $jayaPayConfig['pay_types'] ?? [];
            foreach ($payTypes as $code => $method) {
                if ($method['enabled']) {
                    $paymentMethods[] = [
                        'code' => $code,
                        'name' => $method['name'],
                        'type' => $method['type'],
                        'enabled' => $method['enabled'],
                        'requires_bank_code' => $method['requires_bank_code'] ?? false
                    ];
                }
            }

            return [
                'code' => 1,
                'msg' => $texts['messages']['success'],
                'data' => [
                    'payment_methods' => $paymentMethods,
                    'countries' => $jayaPayConfig['countries'],
                    'bank_codes' => $jayaPayConfig['bank_codes']
                ]
            ];

        } catch (\Exception $e) {
            return [
                'code' => 0,
                'msg' => $texts['messages']['get_payment_methods_failed'] . ': ' . $e->getMessage()
            ];
        }
    }

    /**
     * 创建充值订单
     */
    public function createRechargeOrder($params)
    {
        $lang = $params['lang'] ?? 'id';
        $texts = $this->getTexts($lang);

        try {
            // 参数验证 - JayaPay不需要pay_type参数
            $required = ['token', 'recharge_id', 'amount'];
            foreach ($required as $field) {
                if (!isset($params[$field]) || empty($params[$field])) {
                    return ['code' => 0, 'msg' => $texts['messages']['param_required'] . ": {$field}"];
                }
            }

            // 验证用户token
            $token = $params['token'];
            $userArr = explode(',', auth_code($token, 'DECODE'));
            $uid = $userArr[0] ?? 0;
            $username = $userArr[1] ?? '';

            if (empty($uid)) {
                return ['code' => 0, 'msg' => $texts['messages']['invalid_login']];
            }

            // 验证用户状态
            $user = model('Users')->where('id', $uid)->find();
            if (!$user || $user['state'] != 1) {
                return ['code' => 0, 'msg' => $texts['messages']['user_disabled']];
            }

            // 验证充值渠道
            $rechargeType = model('RechangeType')->where(['id' => $params['recharge_id'], 'mode' => 'jaya_pay', 'state' => 1])->find();
            if (!$rechargeType) {
                return ['code' => 0, 'msg' => $texts['messages']['channel_not_found']];
            }

            // 验证金额
            $amount = floatval($params['amount']);
            if ($amount < $rechargeType['minPrice'] || $amount > $rechargeType['maxPrice']) {
                return ['code' => 0, 'msg' => $texts['messages']['amount_out_of_range']];
            }

            // 计算到账金额
            $fee = $amount * $rechargeType['fee'] / 100;
            $daozhangMoney = $amount - $fee;

            // 生成订单号
            $orderNo = $this->generateOrderNo('JP');

            // 创建充值记录
            $rechargeData = [
                'uid' => $uid,
                'order_number' => $orderNo,
                'money' => $amount,
                'daozhang_money' => $daozhangMoney,
                'fee' => $fee,
                'type' => $params['recharge_id'],
                'add_time' => time(),
                'state' => 0,
                'remarks' => 'JayaPay充值订单'
            ];

            $rechargeId = model('UserRecharge')->insertGetId($rechargeData);
            
            if (!$rechargeId) {
                return ['code' => 0, 'msg' => $texts['messages']['order_create_failed']];
            }

            // 构建JayaPay充值订单数据
            $orderData = [
                'recharge_id' => $params['recharge_id'],
                'order_no' => $orderNo,
                'amount' => $amount,
                'notify_url' => $this->getNotifyUrl($params['recharge_id'])
            ];

            // JayaPay不需要pay_type，如果传递了就使用
            if (!empty($params['pay_type'])) {
                $orderData['pay_type'] = $params['pay_type'];
            }

            // 如果前端传递了bank_code，添加到订单数据中
            if (!empty($params['bank_code'])) {
                $orderData['bank_code'] = $params['bank_code'];
                \think\facade\Log::info('JayaPay received bank_code: ' . $params['bank_code']);
            }

            // 直接调用JayaPay充值API
            $result = $this->createJayaPayRechargeOrder($orderData);

            if (isset($result['code']) && $result['code'] == 1) {
                // 检查是否有支付URL
                if (!isset($result['data']) || !is_array($result['data']) || !isset($result['data']['pay_url'])) {
                    // 删除失败的订单
                    model('UserRecharge')->where('id', $rechargeId)->delete();
                    return [
                        'code' => 0,
                        'msg' => $texts['messages']['payment_url_missing']
                    ];
                }

                // 更新充值记录
                model('UserRecharge')->where('id', $rechargeId)->update([
                    'submitUrl' => $result['data']['pay_url']
                ]);

                return [
                    'code' => 1,
                    'msg' => $texts['messages']['order_created'],
                    'data' => [
                        'order_no' => $orderNo,
                        'pay_url' => $result['data']['pay_url'],
                        'amount' => $amount,
                        'recharge_id' => $rechargeId,
                        'platform_order_no' => $result['data']['platform_order_no'] ?? ''
                    ]
                ];
            } else {
                // 删除失败的订单
                model('UserRecharge')->where('id', $rechargeId)->delete();
                return [
                    'code' => 0,
                    'msg' => $result['msg'] ?? $texts['messages']['order_create_failed']
                ];
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay createRechargeOrder error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => $texts['messages']['system_error'] . ': ' . $e->getMessage()];
        }
    }

    /**
     * 创建提现订单
     */
    public function createWithdrawal($params)
    {
        $lang = $params['lang'] ?? 'id';
        $texts = $this->getTexts($lang);

        try {
            // 参数验证
            $required = ['token', 'withdrawal_id', 'amount', 'bank_code', 'account_number', 'account_name', 'mobile', 'email'];
            foreach ($required as $field) {
                if (!isset($params[$field]) || empty($params[$field])) {
                    return ['code' => 0, 'msg' => $texts['messages']['param_required'] . ": {$field}"];
                }
            }

            // 验证用户token
            $userArr = explode(',', auth_code($params['token'], 'DECODE'));
            $uid = $userArr[0] ?? 0;

            if (empty($uid)) {
                return ['code' => 0, 'msg' => $texts['messages']['invalid_login']];
            }

            // 获取提现记录
            $withdrawal = model('UserWithdrawals')->where('id', $params['withdrawal_id'])->find();
            if (!$withdrawal || $withdrawal['uid'] != $uid) {
                return ['code' => 0, 'msg' => '提现记录不存在'];
            }

            // 获取JayaPay配置
            $config = $this->getConfig();
            if (!$config) {
                return ['code' => 0, 'msg' => '支付渠道配置错误'];
            }

            // 生成代付订单号
            $orderNo = $this->generateOrderNo('JC');

            // 构建代付请求参数
            $cashParams = [
                'merchantCode' => $config['merchant_code'],
                'orderNum' => $orderNo,
                'method' => 'Transfer',
                'orderType' => '0', // 法币交易
                'money' => intval($params['amount']), // 必须是整数
                'feeType' => '0', // 账内扣除
                'dateTime' => date('YmdHis'),
                'number' => $params['account_number'],
                'bankCode' => $params['bank_code'],
                'name' => $params['account_name'],
                'mobile' => $params['mobile'],
                'email' => $params['email'],
                'description' => '提现',
                'notifyUrl' => $this->getWithdrawalNotifyUrl()
            ];

            // 生成RSA签名
            $signString = $this->buildSignString($cashParams);
            $sign = $this->generateRSASign($signString, $config['private_key']);
            $cashParams['sign'] = $sign;

            // 发送代付请求
            $apiUrl = $config['gateway_urls']['cash_out'];
            $response = $this->sendRequest($apiUrl, $cashParams);

            if ($response) {
                $result = json_decode($response, true);
                \think\facade\Log::info('JayaPay withdrawal response: ' . $response);
                
                if ($result && isset($result['platRespCode']) && $result['platRespCode'] === 'SUCCESS') {
                    // 更新提现记录
                    model('UserWithdrawals')->where('id', $params['withdrawal_id'])->update([
                        'trade_number' => $orderNo,
                        'state' => 5, // 代付中
                        'process_time' => time(),
                        'remarks' => 'JayaPay代付处理中，平台订单号：' . ($result['platOrderNum'] ?? '')
                    ]);

                    return [
                        'code' => 1,
                        'msg' => '代付订单创建成功',
                        'data' => [
                            'order_no' => $orderNo,
                            'platform_order_no' => $result['platOrderNum'] ?? '',
                            'status' => '处理中'
                        ]
                    ];
                } else {
                    return [
                        'code' => 0,
                        'msg' => $result['platRespMessage'] ?? 'JayaPay代付订单创建失败'
                    ];
                }
            } else {
                return ['code' => 0, 'msg' => 'JayaPay接口请求失败'];
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay createWithdrawal error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '创建代付订单失败：' . $e->getMessage()];
        }
    }

    /**
     * 处理充值回调
     */
    public function handleRechargeCallback($params)
    {
        try {
            // 验证回调签名 - 使用平台公钥验证
            $platformPublicKey = $this->getPlatformPublicKey();
            if (!$platformPublicKey) {
                \think\facade\Log::error('JayaPay platform public key not found');
                return false;
            }

            if (!$this->verifyRSASign($params, $platformPublicKey)) {
                \think\facade\Log::error('JayaPay recharge notify sign verify failed');
                return false;
            }

            // 处理充值回调
            $result = $this->handleRechargeNotify($params);
            return $result;

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay handleRechargeCallback error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理提现回调
     */
    public function handleWithdrawalCallback($params)
    {
        try {
            // 验证回调签名 - 使用平台公钥验证
            $platformPublicKey = $this->getPlatformPublicKey();
            if (!$platformPublicKey) {
                \think\facade\Log::error('JayaPay platform public key not found');
                return false;
            }

            if (!$this->verifyRSASign($params, $platformPublicKey)) {
                \think\facade\Log::error('JayaPay withdrawal notify sign verify failed');
                return false;
            }

            // 处理代付回调
            $result = $this->handleWithdrawalNotify($params);
            return $result;

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay handleWithdrawalCallback error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 生成RSA签名
     */
    public function generateRSASign($data, $privateKey)
    {
        try {
            // 确保私钥格式正确
            if (strpos($privateKey, '-----BEGIN') === false) {
                $privateKey = "-----BEGIN PRIVATE KEY-----\n" . chunk_split($privateKey, 64, "\n") . "-----END PRIVATE KEY-----\n";
            }

            $privateKeyResource = openssl_pkey_get_private($privateKey);
            if (!$privateKeyResource) {
                throw new \Exception('Invalid private key');
            }

            // 获取密钥详情用于分块处理
            $keyDetails = openssl_pkey_get_details($privateKeyResource);
            $keySize = $keyDetails['bits'];
            $maxBlock = intval($keySize / 8) - 11; // RSA加密时的最大块大小

            $encrypted = '';
            if (strlen($data) <= $maxBlock) {
                // 单块加密 - 使用私钥加密
                $result = openssl_private_encrypt($data, $encrypted, $privateKeyResource);
                if (!$result) {
                    throw new \Exception('RSA private encrypt failed');
                }
            } else {
                // 分块加密
                $offset = 0;
                $encryptedBlocks = [];
                while ($offset < strlen($data)) {
                    $block = substr($data, $offset, $maxBlock);
                    $blockEncrypted = '';
                    $result = openssl_private_encrypt($block, $blockEncrypted, $privateKeyResource);
                    if (!$result) {
                        throw new \Exception('RSA private encrypt failed at block ' . count($encryptedBlocks));
                    }
                    $encryptedBlocks[] = $blockEncrypted;
                    $offset += $maxBlock;
                }
                $encrypted = implode('', $encryptedBlocks);
            }

            openssl_free_key($privateKeyResource);
            return base64_encode($encrypted);

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay sign generation error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 验证RSA签名
     */
    public function verifyRSASign($params, $publicKey)
    {
        try {
            if (!isset($params['platSign']) || empty($params['platSign'])) {
                \think\facade\Log::error('JayaPay notify: missing platSign');
                return false;
            }

            $platSign = $params['platSign'];
            $signParams = $params;
            unset($signParams['platSign']);

            // 构建签名字符串
            $signString = $this->buildSignString($signParams);
            
            // 使用平台公钥解密签名
            $decryptedSign = $this->decryptRSASign($platSign, $publicKey);
            
            \think\facade\Log::info('JayaPay verify - sign string: ' . $signString);
            \think\facade\Log::info('JayaPay verify - decrypted sign: ' . $decryptedSign);
            
            return $signString === $decryptedSign;

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay verify sign error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 发送HTTP请求
     */
    public function sendRequest($url, $data)
    {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Accept: application/json'
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode == 200) {
                return $response;
            } else {
                \think\facade\Log::error("JayaPay request failed with HTTP code: {$httpCode}");
                return false;
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay request error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取多语言文本
     */
    public function getTexts($lang)
    {
        $texts = [];
        
        if ($lang == 'cn' || $lang == 'zh') {
            $texts['messages'] = [
                'success' => '成功',
                'param_required' => '缺少必要参数',
                'invalid_login' => '用户登录信息无效',
                'user_disabled' => '用户账户已被禁用',
                'channel_not_found' => '充值渠道不存在或已关闭',
                'amount_out_of_range' => '充值金额超出限制范围',
                'order_create_failed' => '订单创建失败',
                'order_created' => '订单创建成功',
                'payment_url_missing' => '支付链接获取失败',
                'system_error' => '系统错误',
                'get_payment_methods_failed' => '获取支付方式失败'
            ];
        } elseif ($lang == 'en') {
            $texts['messages'] = [
                'success' => 'Success',
                'param_required' => 'Required parameter missing',
                'invalid_login' => 'Invalid user login information',
                'user_disabled' => 'User account is disabled',
                'channel_not_found' => 'Recharge channel not found or disabled',
                'amount_out_of_range' => 'Amount out of allowed range',
                'order_create_failed' => 'Order creation failed',
                'order_created' => 'Order created successfully',
                'payment_url_missing' => 'Payment URL not available',
                'system_error' => 'System error',
                'get_payment_methods_failed' => 'Failed to get payment methods'
            ];
        } else { // 默认印尼语
            $texts['messages'] = [
                'success' => 'Berhasil',
                'param_required' => 'Parameter yang diperlukan hilang',
                'invalid_login' => 'Informasi login pengguna tidak valid',
                'user_disabled' => 'Akun pengguna dinonaktifkan',
                'channel_not_found' => 'Saluran pengisian tidak ditemukan atau dinonaktifkan',
                'amount_out_of_range' => 'Jumlah di luar rentang yang diizinkan',
                'order_create_failed' => 'Pembuatan pesanan gagal',
                'order_created' => 'Pesanan berhasil dibuat',
                'payment_url_missing' => 'URL pembayaran tidak tersedia',
                'system_error' => 'Kesalahan sistem',
                'get_payment_methods_failed' => 'Gagal mendapatkan metode pembayaran'
            ];
        }

        return $texts;
    }

    /**
     * 获取银行代码列表
     */
    public function getBankCodes()
    {
        $config = $this->getConfig();
        return $config['bank_codes'] ?? [];
    }

    /**
     * 格式化金额
     */
    public function formatAmount($amount)
    {
        return intval($amount); // JayaPay要求整数金额
    }

    /**
     * 获取回调地址
     */
    private function getNotifyUrl($rechargeId)
    {
        // 优先从配置文件获取统一回调URL
        $notifyUrl = $this->getNotifyUrlFromConfig();

        if ($notifyUrl) {
            return $notifyUrl;
        }

        // 兜底方案：从全局配置获取默认域名生成统一充值回调地址
        try {
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (file_exists($configPath)) {
                $config = include $configPath;
                $defaultDomain = $config['global']['default_notify_domain'] ?? 'http://localhost';
                return rtrim($defaultDomain, '/') . '/api/transaction/unifiedCallback';
            }
        } catch (\Exception $e) {
            // 配置文件读取失败，使用最终兜底方案
        }

        // 最终兜底方案：自动检测当前服务器信息
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? 'localhost';

        return $protocol . '://' . $host . '/api/transaction/unifiedCallback';
    }

    /**
     * 从配置文件获取回调URL
     */
    private function getNotifyUrlFromConfig()
    {
        try {
            // 直接读取配置文件
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (!file_exists($configPath)) {
                return null;
            }

            $paymentConfig = include($configPath);

            // 直接使用全局统一充值回调地址
            return $paymentConfig['global']['unified_recharge_callback_url'] ?? null;

        } catch (\Exception $e) {
            \think\facade\Log::error("Failed to get notify URL from config: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取代付回调URL
     */
    private function getWithdrawalNotifyUrl()
    {
        try {
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (file_exists($configPath)) {
                $config = include $configPath;
                $notifyUrl = $config['global']['unified_withdrawal_callback_url'] ?? null;
                if ($notifyUrl) {
                    return $notifyUrl;
                }
            }
        } catch (\Exception $e) {
            // 配置文件读取失败，使用兜底方案
        }

        // 兜底方案：自动检测当前服务器信息
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? 'localhost';

        return $protocol . '://' . $host . '/api/transaction/unifiedWithdrawalCallback';
    }

    /**
     * 构建JayaPay签名字符串
     */
    private function buildSignString($params)
    {
        // 移除签名参数（发送请求时移除sign，验证回调时移除platSign）
        unset($params['sign']);
        unset($params['platSign']);

        // 过滤空值
        $params = array_filter($params, function($value) {
            return $value !== '' && $value !== null;
        });

        // 按Key的ASCII码排序
        ksort($params);

        // 只取参数值进行拼接
        $signString = '';
        foreach ($params as $value) {
            $signString .= $value;
        }

        \think\facade\Log::info('JayaPay sign string: ' . $signString);

        return $signString;
    }

    /**
     * 解密JayaPay签名
     */
    private function decryptRSASign($encryptedSign, $publicKey)
    {
        try {
            // 确保公钥格式正确
            if (strpos($publicKey, '-----BEGIN') === false) {
                $publicKey = "-----BEGIN PUBLIC KEY-----\n" . chunk_split($publicKey, 64, "\n") . "-----END PUBLIC KEY-----\n";
            }

            $publicKeyResource = openssl_pkey_get_public($publicKey);
            if (!$publicKeyResource) {
                throw new \Exception('Invalid public key');
            }

            // Base64解码
            $encryptedData = base64_decode($encryptedSign);
            
            // 获取密钥详情用于分块处理
            $keyDetails = openssl_pkey_get_details($publicKeyResource);
            $keySize = $keyDetails['bits'];
            $maxBlock = intval($keySize / 8); // RSA解密时的最大块大小

            $decrypted = '';
            if (strlen($encryptedData) <= $maxBlock) {
                // 单块解密
                $result = openssl_public_decrypt($encryptedData, $decrypted, $publicKeyResource);
                if (!$result) {
                    throw new \Exception('RSA public decrypt failed');
                }
            } else {
                // 分块解密
                $offset = 0;
                $decryptedBlocks = [];
                while ($offset < strlen($encryptedData)) {
                    $block = substr($encryptedData, $offset, $maxBlock);
                    $blockDecrypted = '';
                    $result = openssl_public_decrypt($block, $blockDecrypted, $publicKeyResource);
                    if (!$result) {
                        throw new \Exception('RSA public decrypt failed at block ' . count($decryptedBlocks));
                    }
                    $decryptedBlocks[] = $blockDecrypted;
                    $offset += $maxBlock;
                }
                $decrypted = implode('', $decryptedBlocks);
            }

            openssl_free_key($publicKeyResource);
            return $decrypted;

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay decrypt sign error: ' . $e->getMessage());
            \think\facade\Log::error('JayaPay decrypt sign debug - encryptedSign length: ' . strlen($encryptedSign));
            \think\facade\Log::error('JayaPay decrypt sign debug - publicKey: ' . substr($publicKey, 0, 50) . '...');
            throw $e;
        }
    }

    /**
     * 处理充值回调
     */
    private function handleRechargeNotify($params)
    {
        try {
            $orderNo = $params['orderNum'] ?? '';
            $status = $params['status'] ?? '';
            $amount = $params['payMoney'] ?? 0; // 充值回调使用payMoney字段

            if (empty($orderNo)) {
                \think\facade\Log::error('JayaPay recharge notify: missing order number');
                return false;
            }

            // 查找充值记录
            $recharge = model('UserRecharge')->where('order_number', $orderNo)->find();
            if (!$recharge) {
                \think\facade\Log::error("JayaPay recharge notify: order not found - {$orderNo}");
                return false;
            }

            // 检查订单状态
            if ($recharge['state'] == 1) {
                \think\facade\Log::info("JayaPay recharge notify: order already processed - {$orderNo}");
                return true;
            }

            // 验证金额（JayaPay金额是整数，不支持小数）
            $notifyAmount = intval($amount);
            $orderAmount = intval($recharge['money']);
            if ($notifyAmount !== $orderAmount) {
                \think\facade\Log::error("JayaPay recharge notify: amount mismatch - order: {$orderAmount}, notify: {$notifyAmount}");
                return false;
            }

            // 处理支付结果 - 充值成功状态为SUCCESS
            if ($status === 'SUCCESS') {
                // 支付成功
                return $this->processRechargeSuccess($recharge);
            } else {
                // 支付失败或其他状态
                model('UserRecharge')->where('id', $recharge['id'])->update([
                    'state' => 2,
                    'dispose_time' => time(),
                    'remarks' => 'JayaPay支付失败，状态：' . $status
                ]);
                return true;
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay handleRechargeNotify error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理代付回调
     */
    private function handleWithdrawalNotify($params)
    {
        try {
            $orderNo = $params['orderNum'] ?? '';
            $status = $params['status'] ?? '';
            $amount = $params['money'] ?? 0;

            if (empty($orderNo)) {
                \think\facade\Log::error('JayaPay withdrawal notify: missing order number');
                return false;
            }

            // 查找提现记录 - orderNum对应order_number字段
            $withdrawal = model('UserWithdrawals')->where('order_number', $orderNo)->find();
            if (!$withdrawal) {
                \think\facade\Log::error("JayaPay withdrawal notify: order not found - {$orderNo}");
                return false;
            }

            // 检查订单状态
            if ($withdrawal['state'] == 1) {
                \think\facade\Log::info("JayaPay withdrawal notify: order already processed - {$orderNo}");
                return true;
            }

            // 验证金额（JayaPay金额是整数，不支持小数）
            $notifyAmount = intval($amount);
            $orderAmount = intval($withdrawal['price']);
            if ($notifyAmount !== $orderAmount) {
                \think\facade\Log::error("JayaPay withdrawal notify: amount mismatch - order: {$orderAmount}, notify: {$notifyAmount}");
                return false;
            }

            // 处理代付结果
            if ($status === '2') { // JayaPay代付成功状态为'2'
                // 代付成功
                return $this->processWithdrawalSuccess($withdrawal);
            } else if ($status === '4') { // JayaPay代付失败状态为'4'
                // 代付失败
                return $this->processWithdrawalFailed($withdrawal, 'JayaPay代付失败，状态：' . $status);
            } else {
                // 其他状态（如处理中），记录但不更新订单状态
                \think\facade\Log::info("JayaPay withdrawal notify: status {$status} for order {$orderNo}");
                return true;
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay handleWithdrawalNotify error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理充值成功
     */
    private function processRechargeSuccess($recharge)
    {
        try {
            // 开启事务
            model('UserRecharge')->startTrans();

            // 更新充值记录状态
            model('UserRecharge')->where('id', $recharge['id'])->update([
                'state' => 1,
                'dispose_time' => time(),
                'remarks' => 'JayaPay支付成功'
            ]);

            // 更新用户余额
            model('UserTotal')->where('uid', $recharge['uid'])->setInc('balance', $recharge['daozhang_money']);
            model('UserTotal')->where('uid', $recharge['uid'])->setInc('total_balance', $recharge['daozhang_money']);

            // 获取更新后的余额
            $newBalance = model('UserTotal')->where('uid', $recharge['uid'])->value('balance');

            // 获取用户信息
            $user = model('Users')->where('id', $recharge['uid'])->find();

            // 添加资金流水记录
            $tradeData = [
                'uid' => $recharge['uid'],
                'username' => $user['username'] ?? '',
                'vip_level' => $user['vip_level'] ?? 0,
                'user_type' => $user['user_type'] ?? 2,
                'source_uid' => $recharge['uid'],
                'source_username' => $user['username'] ?? '',
                'order_number' => $recharge['order_number'],
                'trade_number' => $recharge['order_number'],
                'trade_time' => time(),
                'trade_type' => 1, // 充值
                'trade_amount' => $recharge['daozhang_money'],
                'trade_before_balance' => $newBalance - $recharge['daozhang_money'],
                'account_balance' => $newBalance,
                'account_total_balance' => $newBalance,
                'remarks' => 'JayaPay充值',
                'remarks_en' => 'JayaPay Recharge',
                'remarks_id' => 'Top Up JayaPay',
                'remarks_ft' => 'JayaPay充值',
                'remarks_yd' => 'JayaPay रिचार्ज',
                'remarks_vi' => 'Nạp tiền JayaPay',
                'remarks_es' => 'Recarga JayaPay',
                'remarks_ja' => 'JayaPayチャージ',
                'remarks_th' => 'เติมเงิน JayaPay',
                'remarks_ma' => 'Top Up JayaPay',
                'remarks_pt' => 'Recarga JayaPay',
                'state' => 1,
                'isadmin' => 2,
                'types' => 1 // 用户类型
            ];

            model('UserTransaction')->insert($tradeData);

            // 提交事务
            model('UserRecharge')->commit();

            \think\facade\Log::info("JayaPay recharge success: {$recharge['order_number']}, amount: {$recharge['daozhang_money']}");
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            model('UserRecharge')->rollback();
            \think\facade\Log::error("JayaPay recharge success transaction error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理提现成功
     */
    private function processWithdrawalSuccess($withdrawal)
    {
        try {
            // 开启事务
            model('UserWithdrawals')->startTrans();

            // 更新提现记录状态
            model('UserWithdrawals')->where('id', $withdrawal['id'])->update([
                'state' => 1, // 成功
                'set_time' => time(),
                'remarks' => 'JayaPay代付成功'
            ]);

            // 提交事务
            model('UserWithdrawals')->commit();

            \think\facade\Log::info("JayaPay withdrawal success: {$withdrawal['trade_number']}, amount: {$withdrawal['price']}");
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            model('UserWithdrawals')->rollback();
            \think\facade\Log::error("JayaPay withdrawal success transaction error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理提现失败
     */
    private function processWithdrawalFailed($withdrawal, $reason = '代付失败')
    {
        try {
            // 开启事务
            model('UserWithdrawals')->startTrans();

            // 更新提现记录状态
            model('UserWithdrawals')->where('id', $withdrawal['id'])->update([
                'state' => 2, // 失败
                'set_time' => time(),
                'remarks' => $reason
            ]);

            // 退还用户余额
            model('UserTotal')->where('uid', $withdrawal['uid'])->setInc('balance', $withdrawal['price'] + $withdrawal['fee']);

            // 添加资金流水
            $userTotal = model('UserTotal')->where('uid', $withdrawal['uid'])->find();
            model('UserTransaction')->insert([
                'uid' => $withdrawal['uid'],
                'type' => 1, // 退款
                'money' => $withdrawal['price'] + $withdrawal['fee'],
                'balance' => $userTotal['balance'],
                'remarks' => '提现失败退款：' . $withdrawal['trade_number'],
                'time' => time()
            ]);

            // 提交事务
            model('UserWithdrawals')->commit();

            \think\facade\Log::info("JayaPay withdrawal failed: {$withdrawal['trade_number']}, reason: {$reason}");
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            model('UserWithdrawals')->rollback();
            \think\facade\Log::error("JayaPay withdrawal failed transaction error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 管理后台直接代付方法(绕过用户验证)
     */
    public function processWithdrawalDirect($withdrawalData, $channel)
    {
        try {
            // 获取JayaPay配置
            $config = $this->getConfig();
            if (!$config) {
                return ['code' => 0, 'msg' => 'JayaPay配置不完整'];
            }

            // 构建JayaPay代付请求参数 - 按照官方文档格式
            $params = [
                'merchantCode' => $config['merchant_code'],
                'orderType' => '0', // 法币交易
                'method' => 'Transfer', // 代付方式
                'orderNum' => $withdrawalData['order_number'],
                'money' => (string)intval(floatval($withdrawalData['price'])), // JayaPay要求整数格式
                'currency' => 'IDR',
                'feeType' => '1', // 手续费另计
                'bankCode' => $this->getBankCodeFromCardNumber($withdrawalData['card_number']),
                'number' => $withdrawalData['card_number'],
                'name' => $withdrawalData['card_name'],
                'mobile' => '************', // 默认手机号
                'email' => '<EMAIL>', // 默认邮箱
                'notifyUrl' => $this->getWithdrawalNotifyUrl(),
                'dateTime' => date('YmdHis'),
                'description' => '代付下单'
            ];

            // 生成签名字符串并排序
            $signString = $this->buildSignString($params);
            $params['sign'] = $this->generateRSASign($signString, $config['private_key']);

            // 发送JayaPay代付请求
            $withdrawalUrl = $config['gateway_urls']['cash_out'];
            $response = $this->sendRequest($withdrawalUrl, $params);

            if ($response) {
                $result = json_decode($response, true);
                if ($result && isset($result['platRespCode']) && $result['platRespCode'] == 'SUCCESS') {
                    \think\facade\Log::info('JayaPay withdrawal direct success: ' . $response);
                    return ['code' => 1, 'msg' => '代付请求已提交', 'data' => $result];
                } else {
                    $errorMsg = $result['platRespMessage'] ?? '代付请求失败';
                    \think\facade\Log::error('JayaPay withdrawal direct failed: ' . $response);
                    return ['code' => 0, 'msg' => $errorMsg];
                }
            } else {
                return ['code' => 0, 'msg' => '接口请求失败'];
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay processWithdrawalDirect error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'JayaPay代付异常：' . $e->getMessage()];
        }
    }

    /**
     * 根据卡号获取银行编码
     */
    private function getBankCodeFromCardNumber($cardNumber, $channel = 'jayapay')
    {
        // 从配置文件读取银行编码映射
        try {
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (file_exists($configPath)) {
                $paymentConfig = include($configPath);
                $bankMapping = $paymentConfig['bank_code_mapping'] ?? [];

                // 根据卡号前缀判断银行类型（简化处理）
                // 实际项目中应该根据真实的卡号前缀规则判断
                $bankName = 'Bank Central Asia'; // 默认BCA

                // 根据渠道返回对应的银行编码
                if (isset($bankMapping[$bankName])) {
                    if ($channel === 'jayapay') {
                        return $bankMapping[$bankName]['jayapay'];
                    } else {
                        return $bankMapping[$bankName]['watchpay'];
                    }
                }
            }
        } catch (\Exception $e) {
            \think\facade\Log::error('getBankCodeFromCardNumber error: ' . $e->getMessage());
        }

        // 默认返回值
        return $channel === 'jayapay' ? '014' : 'BCA';
    }

    /**
     * 生成订单号
     */
    private function generateOrderNo($prefix = 'JP')
    {
        return $prefix . date('YmdHis') . mt_rand(100000, 999999);
    }

    /**
     * 创建JayaPay充值订单（直接调用API）
     */
    private function createJayaPayRechargeOrder($orderData)
    {
        try {
            // 获取JayaPay配置
            $config = $this->getConfig();
            if (!$config) {
                return ['code' => 0, 'msg' => 'JayaPay配置不完整'];
            }

            // 构建JayaPay充值请求参数 - 按照官方文档法币代付参数
            $params = [
                'merchantCode' => $config['merchant_code'],
                'orderType' => '0', // 法币交易
                'orderNum' => $orderData['order_no'],
                'payMoney' => intval(floatval($orderData['amount'])), // JayaPay要求整数格式
                'productDetail' => '在线充值', // 商品详情，必需参数
                'notifyUrl' => $orderData['notify_url'],
                'dateTime' => date('YmdHis'),
                'expiryPeriod' => 1440, // 订单过期时间(分钟)，24小时
                'name' => 'User', // 客户名称，必需参数
                'email' => '<EMAIL>', // 用户邮箱，必需参数
                'phone' => '************' // 用户手机号码，必需参数
            ];

            // 如果有支付类型，添加到参数中
            if (!empty($orderData['pay_type'])) {
                $params['method'] = $orderData['pay_type'];
            }

            // 如果有银行编码，添加到参数中
            if (!empty($orderData['bank_code'])) {
                $params['bankCode'] = $orderData['bank_code'];
            }

            // 生成签名字符串并排序
            $signString = $this->buildSignString($params);
            $params['sign'] = $this->generateRSASign($signString, $config['private_key']);

            // 发送JayaPay充值请求
            $rechargeUrl = $config['gateway_urls']['cash_in'];
            $response = $this->sendRequest($rechargeUrl, $params);

            if ($response) {
                $result = json_decode($response, true);
                if ($result && isset($result['platRespCode']) && $result['platRespCode'] == 'SUCCESS') {
                    \think\facade\Log::info('JayaPay recharge order created: ' . $response);
                    return [
                        'code' => 1, 
                        'msg' => '充值订单创建成功', 
                        'data' => [
                            'pay_url' => $result['url'] ?? '', // JayaPay返回的字段名是url，不是payUrl
                            'platform_order_no' => $result['platOrderNum'] ?? ''
                        ]
                    ];
                } else {
                    $errorMsg = $result['platRespMessage'] ?? '充值订单创建失败';
                    \think\facade\Log::error('JayaPay recharge order failed: ' . $response);
                    return ['code' => 0, 'msg' => $errorMsg];
                }
            } else {
                return ['code' => 0, 'msg' => '接口请求失败'];
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay createJayaPayRechargeOrder error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'JayaPay充值异常：' . $e->getMessage()];
        }
    }
}
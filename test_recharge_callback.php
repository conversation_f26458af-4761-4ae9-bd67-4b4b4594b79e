<?php
/**
 * 测试JayaPay充值回调
 * 模拟不同的充值场景
 */

echo "=== 测试JayaPay充值回调 ===\n\n";

// 测试场景1：新的充值订单
$testCases = [
    [
        'name' => '测试场景1：新充值订单 - 100,000 IDR',
        'data' => [
            "msg" => "SUCCESS",
            "code" => "00", 
            "method" => "QRIS",
            "orderNum" => "JP20250803153200" . rand(100000, 999999),
            "platOrderNum" => "PT" . strtoupper(substr(md5(time()), 0, 14)),
            "payFee" => "4500.50",
            "payMoney" => "100000",
            "phone" => "************",
            "name" => "TestUser",
            "platSign" => "ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=",
            "email" => "<EMAIL>",
            "status" => "SUCCESS"
        ]
    ],
    [
        'name' => '测试场景2：小额充值 - 25,000 IDR',
        'data' => [
            "msg" => "SUCCESS",
            "code" => "00", 
            "method" => "BANK_TRANSFER",
            "orderNum" => "JP20250803153300" . rand(100000, 999999),
            "platOrderNum" => "PT" . strtoupper(substr(md5(time() + 1), 0, 14)),
            "payFee" => "1125.25",
            "payMoney" => "25000",
            "phone" => "************",
            "name" => "SmallUser",
            "platSign" => "ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=",
            "email" => "<EMAIL>",
            "status" => "SUCCESS"
        ]
    ],
    [
        'name' => '测试场景3：大额充值 - 500,000 IDR',
        'data' => [
            "msg" => "SUCCESS",
            "code" => "00", 
            "method" => "E_WALLET",
            "orderNum" => "JP20250803153400" . rand(100000, 999999),
            "platOrderNum" => "PT" . strtoupper(substr(md5(time() + 2), 0, 14)),
            "payFee" => "22500.75",
            "payMoney" => "500000",
            "phone" => "081555666777",
            "name" => "BigUser",
            "platSign" => "ZcEJYv3yWINeoTCx6Zy12bWvYWOzi967FtelgdL7v4HUvaLI8DOFVHb34m2ZvUEt/tTCu1ZzGaUFJfI5fDSNz2mnkjR8GqTeEZdDH3qLILprQWUA2ULtfAlYpOxEdR6jYkNeaMRicYlb8XH64D3PjBBaaHdaPPh8BhIeU6rv5js=",
            "email" => "<EMAIL>",
            "status" => "SUCCESS"
        ]
    ]
];

$url = 'http://dianzhan_nginx/api/transaction/unifiedCallback';

foreach ($testCases as $index => $testCase) {
    echo "=== {$testCase['name']} ===\n";
    echo "订单号: {$testCase['data']['orderNum']}\n";
    echo "平台订单号: {$testCase['data']['platOrderNum']}\n";
    echo "金额: {$testCase['data']['payMoney']} IDR\n";
    echo "支付方式: {$testCase['data']['method']}\n";
    echo "用户: {$testCase['data']['name']} ({$testCase['data']['email']})\n\n";

    $postData = http_build_query($testCase['data']);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
        'User-Agent: JayaPay-Callback/1.0'
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    echo "HTTP状态码: {$httpCode}\n";
    
    if ($error) {
        echo "❌ CURL错误: {$error}\n";
    } else {
        echo "响应内容: {$response}\n";
        
        if ($httpCode === 200) {
            if ($response === 'SUCCESS') {
                echo "✅ 回调处理成功！\n";
            } else {
                echo "⚠️ 回调接收成功，但处理结果: {$response}\n";
            }
        } else {
            echo "❌ 回调请求失败\n";
        }
    }
    
    echo "\n" . str_repeat("-", 60) . "\n\n";
    
    // 等待1秒避免请求过快
    sleep(1);
}

echo "=== 所有充值回调测试完成 ===\n";
